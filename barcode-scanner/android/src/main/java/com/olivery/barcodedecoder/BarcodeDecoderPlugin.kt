package com.olivery.BarcodeScanner

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Rect
import android.util.Base64
import com.getcapacitor.JSObject
import com.getcapacitor.Plugin
import com.getcapacitor.PluginCall
import com.getcapacitor.PluginMethod
import com.getcapacitor.annotation.CapacitorPlugin
import com.google.mlkit.vision.barcode.BarcodeScanner
import com.google.mlkit.vision.barcode.BarcodeScannerOptions
import com.google.mlkit.vision.barcode.BarcodeScanning
import com.google.mlkit.vision.barcode.common.Barcode
import com.google.mlkit.vision.common.InputImage
import java.util.regex.Pattern
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.ColorMatrix
import android.graphics.ColorMatrixColorFilter
import android.graphics.Matrix
import kotlin.math.max
import kotlin.math.min

@CapacitorPlugin(name = "BarcodeScanner")
class BarcodeScannerPlugin : Plugin() {

    private lateinit var barcodeScanner: BarcodeScanner

    // Error codes matching TypeScript definitions
    companion object {
        const val ERROR_UNSUPPORTED_FORMAT = "UNSUPPORTED_FORMAT"
        const val ERROR_INVALID_INPUT = "INVALID_INPUT"
        const val ERROR_NO_BARCODE_FOUND = "NO_BARCODE_FOUND"
        const val ERROR_PLATFORM_ERROR = "PLATFORM_ERROR"

        // Base64 validation pattern
        private val BASE64_PATTERN = Pattern.compile("^[A-Za-z0-9+/]*={0,2}$")

        // Maximum image size to prevent memory issues (10MB)
        private const val MAX_IMAGE_SIZE_BYTES = 10 * 1024 * 1024
        
        // Minimum image size for processing
        private const val MIN_IMAGE_SIZE = 32
        
        // Target size for image processing (balance between quality and performance)
        private const val TARGET_MAX_SIZE = 1024
    }

    override fun load() {
        super.load()
        try {
            // Initialize ML Kit barcode scanner with all supported formats
            val options = BarcodeScannerOptions.Builder()
                .setBarcodeFormats(
                    Barcode.FORMAT_QR_CODE,
                    Barcode.FORMAT_CODE_128,
                    Barcode.FORMAT_CODE_39,
                    Barcode.FORMAT_EAN_13,
                    Barcode.FORMAT_UPC_A,
                    Barcode.FORMAT_DATA_MATRIX,
                    Barcode.FORMAT_PDF417,
                    Barcode.FORMAT_AZTEC
                )
                .build()

            barcodeScanner = BarcodeScanning.getClient(options)
        } catch (exception: Exception) {
            // Log initialization error but don't crash
            android.util.Log.e("BarcodeScannerPlugin", "Failed to initialize ML Kit scanner", exception)
        }
    }

    /**
     * Enhanced image preprocessing with multiple enhancement techniques
     */
    private fun preprocessImage(original: Bitmap): List<Bitmap> {
        val processedImages = mutableListOf<Bitmap>()
        
        // 1. Original image (resized if needed)
        val resized = resizeImageIfNeeded(original)
        processedImages.add(resized)
        
        // 2. Grayscale with enhanced contrast
        val grayscale = createGrayscaleEnhanced(resized)
        processedImages.add(grayscale)
        
        // 3. High contrast black and white
        val blackWhite = createBlackWhite(resized)
        processedImages.add(blackWhite)
        
        // 4. Sharpened image
        val sharpened = createSharpened(resized)
        processedImages.add(sharpened)
        
        // 5. Histogram equalized image
        val equalized = createHistogramEqualized(resized)
        processedImages.add(equalized)
        
        return processedImages
    }

    private fun resizeImageIfNeeded(bitmap: Bitmap): Bitmap {
        val maxDim = max(bitmap.width, bitmap.height)
        return if (maxDim > TARGET_MAX_SIZE) {
            val scale = TARGET_MAX_SIZE.toFloat() / maxDim
            val newWidth = (bitmap.width * scale).toInt()
            val newHeight = (bitmap.height * scale).toInt()
            
            if (newWidth > MIN_IMAGE_SIZE && newHeight > MIN_IMAGE_SIZE) {
                Bitmap.createScaledBitmap(bitmap, newWidth, newHeight, true)
            } else {
                bitmap
            }
        } else {
            bitmap
        }
    }

    private fun createGrayscaleEnhanced(input: Bitmap): Bitmap {
        val result = Bitmap.createBitmap(input.width, input.height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(result)
        val paint = Paint()
        
        // Convert to grayscale
        val grayscaleMatrix = ColorMatrix().apply { setSaturation(0f) }
        
        // Enhance contrast and brightness
        val contrast = 1.8f
        val brightness = -20f
        val contrastMatrix = ColorMatrix(floatArrayOf(
            contrast, 0f, 0f, 0f, brightness,
            0f, contrast, 0f, 0f, brightness,
            0f, 0f, contrast, 0f, brightness,
            0f, 0f, 0f, 1f, 0f
        ))
        
        grayscaleMatrix.postConcat(contrastMatrix)
        paint.colorFilter = ColorMatrixColorFilter(grayscaleMatrix)
        canvas.drawBitmap(input, 0f, 0f, paint)
        
        return result
    }

    private fun createBlackWhite(input: Bitmap): Bitmap {
        val result = Bitmap.createBitmap(input.width, input.height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(result)
        val paint = Paint()
        
        // Extreme contrast for black and white effect
        val contrast = 3.0f
        val translate = (-0.5f * contrast + 0.5f) * 255f
        val matrix = ColorMatrix(floatArrayOf(
            contrast, contrast, contrast, 0f, translate,
            contrast, contrast, contrast, 0f, translate,
            contrast, contrast, contrast, 0f, translate,
            0f, 0f, 0f, 1f, 0f
        ))
        
        paint.colorFilter = ColorMatrixColorFilter(matrix)
        canvas.drawBitmap(input, 0f, 0f, paint)
        
        return result
    }

    private fun createSharpened(input: Bitmap): Bitmap {
        val result = Bitmap.createBitmap(input.width, input.height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(result)
        val paint = Paint()
        
        // Sharpen filter matrix
        val sharpenMatrix = ColorMatrix(floatArrayOf(
            0f, -0.25f, 0f, 0f, 0f,
            -0.25f, 2f, -0.25f, 0f, 0f,
            0f, -0.25f, 0f, 0f, 0f,
            0f, 0f, 0f, 1f, 0f,
            0f, 0f, 0f, 0f, 1f
        ))
        
        paint.colorFilter = ColorMatrixColorFilter(sharpenMatrix)
        canvas.drawBitmap(input, 0f, 0f, paint)
        
        return result
    }

    private fun createHistogramEqualized(input: Bitmap): Bitmap {
        val result = Bitmap.createBitmap(input.width, input.height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(result)
        val paint = Paint()
        
        // Simple histogram equalization approximation using ColorMatrix
        val matrix = ColorMatrix(floatArrayOf(
            1.2f, 0f, 0f, 0f, 30f,
            0f, 1.2f, 0f, 0f, 30f,
            0f, 0f, 1.2f, 0f, 30f,
            0f, 0f, 0f, 1f, 0f
        ))
        
        paint.colorFilter = ColorMatrixColorFilter(matrix)
        canvas.drawBitmap(input, 0f, 0f, paint)
        
        return result
    }

    override fun handleOnDestroy() {
        super.handleOnDestroy()
        // Clean up resources
        try {
            if (::barcodeScanner.isInitialized) {
                barcodeScanner.close()
            }
        } catch (exception: Exception) {
            android.util.Log.w("BarcodeScannerPlugin", "Error closing barcode scanner", exception)
        }
    }

    @PluginMethod
    fun scanFromImage(call: PluginCall) {
        val base64Image = call.getString("base64")

        // Comprehensive input validation
        val validationError = validateInput(base64Image)
        if (validationError != null) {
            rejectWithError(call, validationError.first, validationError.second)
            return
        }

        // Check if scanner is initialized
        if (!::barcodeScanner.isInitialized) {
            rejectWithError(call, ERROR_PLATFORM_ERROR, "Barcode scanner not initialized")
            return
        }

        var originalBitmap: Bitmap? = null
        val processedBitmaps = mutableListOf<Bitmap>()
        
        try {
            // Convert base64 to Bitmap with enhanced validation
            originalBitmap = base64ToBitmap(base64Image!!)
            if (originalBitmap == null) {
                rejectWithError(call, ERROR_INVALID_INPUT, "Failed to decode base64 image data")
                return
            }

            // Validate bitmap properties
            if (originalBitmap.isRecycled) {
                rejectWithError(call, ERROR_INVALID_INPUT, "Image bitmap is recycled")
                return
            }

            if (originalBitmap.width <= 0 || originalBitmap.height <= 0) {
                rejectWithError(call, ERROR_INVALID_INPUT, "Invalid image dimensions")
                return
            }

            // Process image with multiple enhancement techniques
            val enhancedImages = preprocessImage(originalBitmap)
            processedBitmaps.addAll(enhancedImages)
            
            // Try scanning with each processed image
            scanWithMultipleImages(call, processedBitmaps)

        } catch (exception: Exception) {
            // Clean up bitmaps on exception
            cleanupBitmaps(originalBitmap, processedBitmaps)

            val errorMessage = when (exception) {
                is OutOfMemoryError -> "Insufficient memory to process image"
                is IllegalArgumentException -> "Invalid input parameters: ${exception.message}"
                else -> "Error processing barcode: ${exception.message}"
            }

            rejectWithError(call, ERROR_PLATFORM_ERROR, errorMessage)
        }
    }

    private fun scanWithMultipleImages(call: PluginCall, images: List<Bitmap>) {
        var currentImageIndex = 0
        val allBarcodes = mutableListOf<Barcode>()
        
        fun processNextImage() {
            if (currentImageIndex >= images.size) {
                // All images processed, return best result
                cleanupBitmaps(null, images)
                
                if (allBarcodes.isEmpty()) {
                    rejectWithError(call, ERROR_NO_BARCODE_FOUND, "No barcode detected in the provided image after trying multiple processing techniques")
                } else {
                    // Return the barcode with highest confidence or first valid one
                    val bestBarcode = selectBestBarcode(allBarcodes)
                    val result = createBarcodeResult(bestBarcode)
                    call.resolve(result)
                }
                return
            }

            val currentBitmap = images[currentImageIndex]
            
            try {
                // Create InputImage from current Bitmap
                val inputImage = InputImage.fromBitmap(currentBitmap, 0)
                
                // Process image with ML Kit
                barcodeScanner.process(inputImage)
                    .addOnSuccessListener { barcodes ->
                        try {
                            // Add valid barcodes to our collection
                            barcodes.forEach { barcode ->
                                if (!barcode.rawValue.isNullOrEmpty()) {
                                    allBarcodes.add(barcode)
                                }
                            }
                            
                            // If we found barcodes and this is high-confidence, return immediately
                            if (barcodes.isNotEmpty() && currentImageIndex <= 1) {
                                // Found barcode in original or first enhanced image - high confidence
                                cleanupBitmaps(null, images)
                                val result = createBarcodeResult(barcodes[0])
                                call.resolve(result)
                                return@addOnSuccessListener
                            }
                            
                            // Continue to next image
                            currentImageIndex++
                            processNextImage()
                            
                        } catch (exception: Exception) {
                            android.util.Log.w("BarcodeScannerPlugin", "Error processing barcode results", exception)
                            currentImageIndex++
                            processNextImage()
                        }
                    }
                    .addOnFailureListener { exception ->
                        android.util.Log.w("BarcodeScannerPlugin", "ML Kit processing failed for image $currentImageIndex", exception)
                        currentImageIndex++
                        processNextImage()
                    }
                    
            } catch (exception: Exception) {
                android.util.Log.w("BarcodeScannerPlugin", "Error creating InputImage for index $currentImageIndex", exception)
                currentImageIndex++
                processNextImage()
            }
        }
        
        // Start processing
        processNextImage()
    }
    
    private fun selectBestBarcode(barcodes: List<Barcode>): Barcode {
        // Prioritize by format preference (CODE_128 often more reliable than others)
        val formatPriority = mapOf(
            Barcode.FORMAT_CODE_128 to 1,
            Barcode.FORMAT_QR_CODE to 2,
            Barcode.FORMAT_EAN_13 to 3,
            Barcode.FORMAT_UPC_A to 4,
            Barcode.FORMAT_CODE_39 to 5
        )
        
        return barcodes.minByOrNull { barcode ->
            val formatScore = formatPriority[barcode.format] ?: 99
            val lengthScore = if (barcode.rawValue?.length ?: 0 > 0) 0 else 10
            formatScore + lengthScore
        } ?: barcodes[0]
    }

    private fun cleanupBitmaps(original: Bitmap?, processed: List<Bitmap>) {
        original?.let { bmp ->
            if (!bmp.isRecycled) {
                bmp.recycle()
            }
        }
        
        processed.forEach { bitmap ->
            if (!bitmap.isRecycled) {
                bitmap.recycle()
            }
        }
    }

    private fun validateInput(base64Image: String?): Pair<String, String>? {
        // Check if input is null or empty
        if (base64Image.isNullOrEmpty()) {
            return Pair(ERROR_INVALID_INPUT, "Base64 image data is required")
        }

        // Check minimum length (a valid base64 image should be reasonably long)
        if (base64Image.length < 20) {
            return Pair(ERROR_INVALID_INPUT, "Base64 image data is too short")
        }

        // Extract clean base64 string
        val cleanBase64 = if (base64Image.contains(",")) {
            val parts = base64Image.split(",")
            if (parts.size != 2) {
                return Pair(ERROR_INVALID_INPUT, "Invalid data URL format")
            }

            // Validate data URL prefix
            val prefix = parts[0].lowercase()
            if (!prefix.startsWith("data:image/") || !prefix.contains("base64")) {
                return Pair(ERROR_UNSUPPORTED_FORMAT, "Unsupported image format in data URL")
            }

            parts[1]
        } else {
            base64Image
        }

        // Validate base64 format
        if (!BASE64_PATTERN.matcher(cleanBase64).matches()) {
            return Pair(ERROR_INVALID_INPUT, "Invalid base64 format")
        }

        // Check estimated size to prevent memory issues
        val estimatedSize = (cleanBase64.length * 3) / 4
        if (estimatedSize > MAX_IMAGE_SIZE_BYTES) {
            return Pair(ERROR_INVALID_INPUT, "Image size exceeds maximum allowed size")
        }

        return null // No validation errors
    }

    private fun rejectWithError(call: PluginCall, errorCode: String, message: String) {
        val errorObject = JSObject()
        errorObject.put("code", errorCode)
        errorObject.put("message", message)
        call.reject(message, errorCode, null, errorObject)
    }

    private fun base64ToBitmap(base64String: String): Bitmap? {
        return try {
            // Remove data URL prefix if present (e.g., "data:image/png;base64,")
            val cleanBase64 = if (base64String.contains(",")) {
                base64String.substring(base64String.indexOf(",") + 1)
            } else {
                base64String
            }

            // Validate base64 string length after cleaning
            if (cleanBase64.isEmpty()) {
                return null
            }

            val decodedBytes = try {
                Base64.decode(cleanBase64, Base64.DEFAULT)
            } catch (exception: IllegalArgumentException) {
                // Invalid base64 format
                return null
            }

            // Check if decoded bytes are reasonable
            if (decodedBytes.isEmpty() || decodedBytes.size < 10) {
                return null
            }

            // Decode to bitmap with better options for quality
            val options = BitmapFactory.Options().apply {
                inPreferredConfig = Bitmap.Config.ARGB_8888
                inMutable = true
                inSampleSize = 1 // Don't downsample initially
            }

            val bitmap = BitmapFactory.decodeByteArray(decodedBytes, 0, decodedBytes.size, options)

            // Validate bitmap was created successfully
            if (bitmap == null) {
                return null
            }

            // Additional bitmap validation
            if (bitmap.width <= 0 || bitmap.height <= 0) {
                bitmap.recycle()
                return null
            }

            bitmap
        } catch (exception: Exception) {
            android.util.Log.w("BarcodeScannerPlugin", "Error converting base64 to bitmap", exception)
            null
        }
    }

    private fun createBarcodeResult(barcode: Barcode): JSObject {
        val result = JSObject()

        // Extract barcode value
        val rawValue = barcode.rawValue ?: ""
        if (rawValue.isNotEmpty()) {
            result.put("content", rawValue)
            result.put("hasContent", true)
        } else {
            result.put("hasContent", false)
            result.put("content", "")
        }

        // Map ML Kit format to our format enum
        result.put("format", mapBarcodeFormat(barcode.format))

        // Extract bounding box
        val boundingBox = JSObject()
        barcode.boundingBox?.let { rect ->
            boundingBox.put("x", rect.left)
            boundingBox.put("y", rect.top)
            boundingBox.put("width", rect.width())
            boundingBox.put("height", rect.height())
        }
        result.put("boundingBox", boundingBox)

        // Extract corner points
        val cornerPointsArray = mutableListOf<JSObject>()
        barcode.cornerPoints?.let { points ->
            for (point in points) {
                val cornerPoint = JSObject()
                cornerPoint.put("x", point.x)
                cornerPoint.put("y", point.y)
                cornerPointsArray.add(cornerPoint)
            }
        }
        result.put("cornerPoints", cornerPointsArray)

        return result
    }

    private fun mapBarcodeFormat(mlKitFormat: Int): String {
        return when (mlKitFormat) {
            Barcode.FORMAT_QR_CODE -> "QR_CODE"
            Barcode.FORMAT_CODE_128 -> "CODE_128"
            Barcode.FORMAT_CODE_39 -> "CODE_39"
            Barcode.FORMAT_EAN_13 -> "EAN_13"
            Barcode.FORMAT_UPC_A -> "UPC_A"
            Barcode.FORMAT_DATA_MATRIX -> "DATA_MATRIX"
            Barcode.FORMAT_PDF417 -> "PDF417"
            Barcode.FORMAT_AZTEC -> "AZTEC"
            else -> "UNKNOWN"
        }
    }
}