import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ActionSheetButton, ActionSheetController, AlertButton, AlertController, AlertInput, InfiniteScrollCustomEvent, ModalController, RefresherCustomEvent, SelectCustomEvent, ToastController, ViewWillEnter, ViewWillLeave } from '@ionic/angular';
import { Store } from '@ngrx/store';
import { Observable, Subject, combineLatest, debounceTime, distinctUntilChanged, filter, map, skip, take, takeUntil } from 'rxjs';
import { MoneyCollectionState } from 'src/app/ngrx-store/money-collection/store';
import * as moneyCollectionActions from 'src/app/ngrx-store/money-collection/store/actions';
import * as moneyCollectionSelectors from 'src/app/ngrx-store/money-collection/store/selectors';
import { StatusState } from 'src/app/ngrx-store/status/store';
import { IGroup, RecordCardStructure, RecordStructureService } from 'src/app/services/record-structure-service';
import { MoneyCollectionsEnum } from 'src/app/utils/enums';
import * as statusActions from 'src/app/ngrx-store/status/store/actions';
import * as statusSelector from 'src/app/ngrx-store/status/store/selectors';
import { Storage } from '@ionic/storage-angular';
import { FormControl } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { TimeService } from 'src/app/services/time-services';
import { DateSelectorComponent } from 'src/app/modals/date-selector/date-selector.component';
import { selectLoading } from 'src/app/ngrx-store/user/store';
import { StatusActionService } from 'src/app/services/statusAction';
import { OdooJsonRPC } from 'src/app/services/odooJsonRPC';
import { PrintReportService } from 'src/app/services/print-reports-service';
import { ModelsFilterState } from 'src/app/ngrx-store/models-filter/store';
import * as modelsFilterSelectors from 'src/app/ngrx-store/models-filter/store/selectors';
import { RecordCreatorPage } from 'src/app/modals/record-creator/record-creator.page';
import { dialogService } from 'src/app/services/error-handlar-service';
import { Browser } from '@capacitor/browser';

@Component({
  selector: 'app-collection',
  templateUrl: 'collection.page.html',
  styleUrls: ['collection.page.scss']
})

export class CollectionPage implements  ViewWillLeave ,OnInit{
  modelName: string = '';
  headerTitle: string = '';
  dataOfCollection: any[] = [];
  searchControl: FormControl = new FormControl()
  collectionCardsStructures$!: Observable<RecordCardStructure[]>
  private destroyed$ = new Subject<void>();
  neededFields: any;
  searchDomain: any[] = []
  statusDomain: any[] = []
  groupByDomain: any[] = []
  groupByFields: any=[];
  groupOptionLabels:string []=[]
  selectedGroups:IGroup[]=[]
  selectedGroupOption!: string;
  selectedOption!: {name:string,discription:string};
  isGroupByEnabled:boolean=false
  isGroupOpened:boolean=false
  isScrolling:boolean=false
  groupedCollections:IGroup[]=[]
  groupingLevels: string[] = [];
  moneyCollectionsCount!: number;
  loadedCollectionsCount!: number;
  collectionIds!: number[];
  cardName!: string;
  shownOnSegmentStatuses: any[] = []
  filterFields: any;
  selectedFilterField: any;
  defaultSearchFields: any;
  currentStatusFilter: any = false;
  dateDomain: any = []
  selectedDateFilter: any;
  isChangingStatusFilter: boolean = true
  initeScrollCustomEvent!: InfiniteScrollCustomEvent;
  dateFilter = [{
    name: "TODAY"
  },
  {
    name: "YESTERDAY"
  },
  {
    name: "THIS_WEEK"
  },
  {
    name: "THIS_MONTH"
  },
  {
    name: "THIS_YEAR"
  },
  {
    name: "SELECTED_DATE"
  }
  ]
  userInfo: any;
  collectionType: any;
  nextStatuses: any;
  loading!: boolean;
  collectionId: any;
  isFiltered:boolean=false
  searchFilterPlaceholder:string = 'ALL'
  searchValue: any;
  infiniteScroll=new Subject<any>()
  groupByCardFields:any
  constructor(
    private route: ActivatedRoute,
    private moneyCollectionStore: Store<MoneyCollectionState>,
    private recordStructreService: RecordStructureService,
    private statusStore: Store<StatusState>,
    private storage: Storage,
    private alertController: AlertController,
    private modalCtrl: ModalController,
    private translate: TranslateService,
    private timeService: TimeService,
    private alertCtrl:AlertController,
    private toastCtrl:ToastController,
    private odooRPC:OdooJsonRPC,
    private printService : PrintReportService,
    private router: Router,
    private modelsFilterStore :Store<ModelsFilterState>,
    private statusActionService:StatusActionService,
    private dialogService: dialogService,
    private actionSheetCtrl:ActionSheetController

  ) { }
  ngOnInit(): void {
    this.listenToInfiniteScroll()
  }

  ionViewDidEnter	(): void {
    this.route.queryParams.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(params => {
      this.modelName = params['modelName'];
      this.headerTitle = MoneyCollectionsEnum[this.modelName as keyof typeof MoneyCollectionsEnum];
      this.collectionIds =  params['collectionIds']
      if (this.collectionIds) {
        this.statusDomain = this.statusDomain.concat([['id','in',this.collectionIds]])
      }
      else if(this.currentStatusFilter){
          this.statusDomain = [['state', '=', this.currentStatusFilter]]
      }
      else{
        this.statusDomain = []
        this.isFiltered = false
      }
      this.getDefaultSearchFields()
      this.getFilterFields()
      this.getGroupByFields()
      this.moneyCollectionStore.dispatch(new moneyCollectionActions.LoadDefaultSearchFilter({model:this.modelName}));
      this.moneyCollectionStore.dispatch(new moneyCollectionActions.LoadFilterFields({model:this.modelName}));
      this.cardName = params['cardName']
      this.collectionType = params['collectionType']
      this.getUserInfo()
      this.loadCollections()
      this.initSearch();

    });


  }
  loadCollections() {
    this.recordStructreService.fetchStructure(this.cardName).then(structueResponse => {
      if (structueResponse && structueResponse.success) {
        this.updateCollectionSearch(structueResponse.neededFields)
        this.neededFields = structueResponse.neededFields

        this.collectionCardsStructures$ = this.moneyCollectionStore.select(moneyCollectionSelectors.selectAllDataStructures)
          .pipe(map(structuredCollections => {
            if(this.initeScrollCustomEvent){
              this.initeScrollCustomEvent.target.complete()
            }
            this.loadedCollectionsCount = structuredCollections.length
            this.collectionIds = structuredCollections.map(collection => collection.id)
            
            return structuredCollections
          }))
      }

    })
  }

  loadStatuses() {
    this.statusStore.dispatch(new statusActions.LoadHttp({ search: [['status_type', '=', 'olivery_collection'], ['collection_type', '=', this.collectionType]] }))
    this.statusStore.select(statusSelector.selectCollectionShownOnSegmentStatus(this.userInfo[0].group_id[0],this.collectionType)).pipe(filter(statuses => statuses.length > 0), take(1)).subscribe(statuses => {

      this.shownOnSegmentStatuses = statuses
    })
  }

  handleRefresh(event:RefresherCustomEvent){
    this.loading = true
    this.updateCollectionSearch()
    this.moneyCollectionStore.select(moneyCollectionSelectors.selectLoading).pipe(filter(loading=>!loading),take(1)).subscribe(()=>{
      event.target.complete()
      this.loading=false
    })
  }

  getUserInfo() {
    this.storage.get('user_info').then(userInfo => {
      if (userInfo) {
        this.userInfo = userInfo
        this.loadStatuses()
      }
    })
  }

  async updateCollectionSearch(fields?: any[]) {
    let searchParams: any = { search: await this.getDomain() }
    if (fields) {
      searchParams['fields'] = fields
    }
    this.moneyCollectionStore.dispatch(new moneyCollectionActions.UpdateSearch({ ...searchParams, model: this.modelName, cardName: this.cardName }));
    this.getCountOfCollections()
  }

  async getDomain(): Promise<any[]> {
    const collectionFilter = await this.modelsFilterStore.select(modelsFilterSelectors.selectModelFilterByModelName(this.modelName)).pipe(take(1)).toPromise();
    if (!this.isFiltered) {
      if (collectionFilter.length > 0) {
        return this.statusDomain.concat(this.groupByDomain,this.searchDomain, this.dateDomain, collectionFilter[0].domain);
      } else {
        return this.statusDomain.concat(this.groupByDomain,this.searchDomain, this.dateDomain)
      }
    } 
    else {
      return this.statusDomain.concat(this.groupByDomain, this.searchDomain, this.dateDomain, this.statusDomain);
    }
    
  }

  async getCountOfCollections() {
    this.moneyCollectionStore.dispatch(new moneyCollectionActions.CountNumberOfMoneyCollectionsHttp({ search: await this.getDomain(), model: this.modelName }))
    combineLatest([
      this.moneyCollectionStore.select(moneyCollectionSelectors.selectAllMoneyCollectionsCount),
      this.moneyCollectionStore.select(moneyCollectionSelectors.selectLoadingCount)
    ])
      .pipe(filter(([, loadingCount]) => !loadingCount), take(1)).subscribe(async ([moneyCollectionsCount]) => {

        this.moneyCollectionsCount = moneyCollectionsCount
        this.isChangingStatusFilter = false
      })
  }

  selectDates(event: SelectCustomEvent) {
    if (!!event.detail.value) {
      this.dateDomain = [];
    }
    let startDate;
    let endDate;
    let selectedFilterType;
    const value = event.detail.value;
    this.selectedDateFilter = value;

    const handleDateRange = (startFunc: any, endFunc: any, range: string | null) => {
      startDate = this.timeService.dateTimeToOdooFormat(
        this.timeService.getUTCFormat(startFunc(range))
      );
      endDate = this.timeService.dateTimeToOdooFormat(
        this.timeService.getUTCFormat(endFunc(range))
      );
    };

    switch (value) {
      case 'TODAY':
        handleDateRange(this.timeService.getStartDate, this.timeService.getEndDate, null);
        break;
      case 'YESTERDAY':
        handleDateRange(this.timeService.getYesterdayStartDate, this.timeService.getYesterdayEndDate, null);
        break;
      case 'THIS_WEEK':
        handleDateRange(this.timeService.getStartDate, this.timeService.getEndDate, 'week');
        break;
      case 'THIS_MONTH':
        handleDateRange(this.timeService.getStartDate, this.timeService.getEndDate, 'month');
        break;
      case 'THIS_YEAR':
        handleDateRange(this.timeService.getStartDate, this.timeService.getEndDate, 'year');
        break;
      case 'SELECTED_DATE':
        this.modalCtrl.create({
          component: DateSelectorComponent,
          cssClass: 'windowed-modal',
        }).then((modal) => {
          modal.onDidDismiss().then((dateRange) => {
            if (dateRange && dateRange.data) {
              startDate = dateRange.data.start_date;
              endDate = dateRange.data.end_date;
              selectedFilterType = dateRange.data.selected_filter_type;
              startDate = this.timeService.dateTimeToOdooFormat(startDate);
              endDate = this.timeService.dateTimeToOdooFormat(endDate);
              this.filterByDate(startDate, endDate, selectedFilterType);
            } else {
              this.filterByDate();
            }
            this.selectedDateFilter = false;
          });
          modal.present();
        });
        return;
      default:
        this.filterByDate(startDate, endDate);
        break;
    }

    if (value !== 'SELECTED_DATE' && !!value) {
      this.filterByDate(startDate, endDate);
    }
  }

  filterByDate(startDate?: any, endDate?: any, selectedFilterType?: any) {
    if (startDate && endDate) {
      if (!selectedFilterType) {
        selectedFilterType = 'create_date'
      }
      this.dateDomain.push([selectedFilterType, '>=', startDate], [selectedFilterType, '<=', endDate])

    }


    this.updateCollectionSearch()
  }

  getDefaultSearchFields() {
    this.moneyCollectionStore.select(moneyCollectionSelectors.selectDefaultSearchFilter).subscribe(fields => {
      if (fields.length > 0) {
        this.defaultSearchFields = fields;
      }
      else {
        this.defaultSearchFields = ['sequence']
      }

    });
  }

  getFilterFields() {
    this.moneyCollectionStore.select(moneyCollectionSelectors.selectFilterFields).subscribe(fields => {
      this.filterFields = Object.values(fields);

    });


  }

  filterByStatus(event: SelectCustomEvent) {
    this.isChangingStatusFilter = true
    this.statusDomain = []
    if (event && event.detail && event.detail.value && event.detail.value) {
      const value = event.detail.value;
      this.statusDomain = [['state', '=', value]]
    }
    this.updateCollectionSearch()
  }



  async showFilterOptions() {
    const inputs = this.filterFields.map((field: any) => {
      return {
        name: field.name,
        type: 'radio',
        label: this.translate.instant(field.description),
        value: field,
        checked: this.selectedGroupOption === field.name
      };
    });

    const alert = await this.alertController.create({
      header: this.translate.instant('SELECT_FEILDS_TO_SEARCH'),
      inputs: inputs,
      buttons: [
        {
          text: this.translate.instant('CANCEL'),
          role: this.translate.instant('CANCEL')
        },
        {
          text: this.translate.instant('Ok'),
          handler: (selectedFields) => {
            this.selectedFilterField = selectedFields.name
            this.searchFilterPlaceholder = this.translate.instant(selectedFields.description);
            this.searchControl.reset()
          }
        }
      ],
      mode: 'ios',


    });

    await alert.present();
  }

  async openGroupingCollectionsAlert() {
    const groupedCollectionsAlert = await this.alertController.create({
      header: this.translate.instant('GROUP_BY'),
      inputs: this.groupByFields.map((field: any) => {
        return {
            name: field.description,
            type: 'radio',
            label: this.translate.instant(field.description),
            value: field,
            checked: this.selectedGroupOption === field.name
        };
      }),
      buttons: [
        {
          text: this.translate.instant('CANCEL'),
          role: 'cancel',
        },
        {
          text: this.translate.instant('OK'),
          handler: async (selectedOption) => {
            this.selectedOption = selectedOption;
            this.groupOptionLabels.push(groupedCollectionsAlert.inputs.filter(input=>input.value==selectedOption)[0].label as string) 
            let groupFields = [this.selectedOption.name.split(':')[0]]
            if (this.groupByCardFields) {
              let fields = this.groupByCardFields.map((item: { name: any; }) => item.name)
              groupFields = [...groupFields, ...fields];
            }
            this.moneyCollectionStore.dispatch(new moneyCollectionActions.LoadCollectionsGroupedBy({model:this.modelName, domain: await this.getDomain(), fields: groupFields, groupBy: [this.selectedOption.name] }))
            this.groupCollectionsByOption(selectedOption);
            this.groupByFields = this.groupByFields.filter((item: { name: string; }) => item.name !== selectedOption.name);
          },
        },
      ],
      mode: 'ios',
      animated: true
    });
    await groupedCollectionsAlert.present();
  }

  getRestOfFields(selectedOption:any, groupedOrder:any) {
    let rows:any = []
    for (const key in groupedOrder) {
      if (groupedOrder.hasOwnProperty(key) && key != '__domain') {
        const result = this.groupByCardFields.find((item: { name: string; }) => item.name === key)
        if (result) {
          rows.push({
            'name':result['field_description'],
            'value':groupedOrder[key]
          })
        }
      }
    }
    return rows
  }


  groupCollectionsByOption(selectedOption: {name:string,discription:string}) {
    this.selectedGroupOption = this.groupByFields.filter((field:any)=>selectedOption.name==field.name)[0].description
    this.isGroupByEnabled = true;
    this.groupedCollections = [];
    combineLatest([
      this.moneyCollectionStore.select(moneyCollectionSelectors.selectGroupedMoneyCollections),
      this.moneyCollectionStore.select(moneyCollectionSelectors.selectLoading)
    ])
    .pipe(filter(([groups,loading])=>!loading),take(1)).subscribe(([groupedCollections,loading]) => {
      if (groupedCollections && groupedCollections.length > 0) {
        this.groupedCollections = groupedCollections.map((groupedOrder: any) => ({
            key: Array.isArray(groupedOrder[selectedOption.name]) ? groupedOrder[selectedOption.name][1] : groupedOrder[selectedOption.name],
            keyId: Array.isArray(groupedOrder[selectedOption.name]) ? groupedOrder[selectedOption.name][0] : null,
            recordsLength: groupedOrder[`${selectedOption.name.split(':')[0]}_count`],
            type: 'groupedOrders',
            domain:groupedOrder['__domain'],
            fieldName:selectedOption.name.split(':')[0],
            groupBy:selectedOption.name,
            otherKeys: this.getRestOfFields(selectedOption, groupedOrder)
        }));
        for(let i=0; i<this.groupedCollections.length; i++){
          this.groupedCollections[i]['structure'] = this.recordStructreService.formatForGroup(this.groupedCollections[i])
        }
      }
    })
  }
  openGroupedCollections(group: IGroup) {
    this.selectedGroups.push(group)
    let dateGroupsLength = this.selectedGroups.filter(g=>g.groupBy.includes(':')).length
    this.isScrolling=false
    this.isGroupByEnabled = false
    this.isGroupOpened = true
    this.groupByDomain = this.groupByDomain.concat(group.domain)
    this.groupByDomain = this.groupByDomain
        .filter((domain: any) => domain !== '&' && domain !== '|')
        .filter((domain: any[], idx: number, self: any[]) => {
            return (
                idx === self.findIndex(d => d[0] === domain[0] && d[1] === domain[1] && d[2] === domain[2] )
            );
        });
    this.groupByDomain = this.groupByDomain.slice(0,this.selectedGroups.length+dateGroupsLength)
    this.groupingLevels.push(this.translate.instant(group.key || "UNDEFINED"));
    this.updateCollectionSearch()
    
  }
  closeGroupedCollections(index:number) {
    this.getGroupByFields()
    let removedGroup = this.selectedGroups.splice(index, 1);
    this.groupingLevels.splice(index, 1);
    this.groupOptionLabels.splice(index, 1);

    this.groupByDomain = this.groupByDomain
        .filter((domain: any[]) => {
            // Remove duplicates based on domain field name
            return (
                domain[0] !== removedGroup[0].fieldName
            );
        });

    let fields: string[] = [];
    this.groupByDomain = this.groupByDomain.filter((domain: any[]) => {
        if (domain[0] !== removedGroup[0].fieldName) {
            fields.push(domain[0]);
            return true;
        } else {
            return false;
        }
    });

    this.groupByFields = this.groupByFields.filter(
        (item: { name: string }) => !fields.includes(item.name.split(":")[0])
    );

    this.updateCollectionSearch();

    if (this.groupByDomain.length === 0) {
        this.closeGroupBy();
    }

  }
  closeGroupBy() {
    this.isGroupOpened = false;
    this.isGroupByEnabled = false;
    this.groupByDomain = []
    this.groupingLevels= []
    this.groupOptionLabels =[]
    this.getGroupByFields()
    this.updateCollectionSearch()
  }

  getGroupByFields(){
    this.moneyCollectionStore.select(moneyCollectionSelectors.selectGroupByFields).subscribe(fields => {
      this.groupByFields = Object.values(fields);
    });
    this.storage.get('user_info').then(userInfo=>{
      if(userInfo){
        this.odooRPC.call('rb_delivery.group_by_card_configurations', 'get_group_by_card', [this.modelName, userInfo[0].group_id[0]]).then(async response=>{
          if (response&&response.body&&response.body.result&&response.body.result.result) {
            this.groupByCardFields=response.body.result.result
          }
        })
      }
    })
  }

  listenToInfiniteScroll(){
    this.infiniteScroll.pipe(debounceTime(1000),takeUntil(this.destroyed$)).subscribe(event=>{
      this.isScrolling = true
      this.moneyCollectionStore.dispatch(new moneyCollectionActions.LoadHttp({model:this.modelName, cardName: this.cardName }))
      this.initeScrollCustomEvent = event as InfiniteScrollCustomEvent
    })
  }
  

  private performSearch(value: string): void {
    this.searchDomain = []
    if (value) {
      const valueToSearch = value.toLowerCase();
      const searchFields = this.selectedFilterField ? [this.selectedFilterField] : this.defaultSearchFields;

      this.searchDomain = this.generateSearchDomain(searchFields, valueToSearch);

    }

    this.updateCollectionSearch(this.neededFields)


  }

  generateSearchDomain(fields: string[], search: string): any[] {
    const domain = [];

    for (let i = 0; i < fields.length - 1; i++) {
      domain.push('|');
    }

    for (const field of fields) {
      domain.push([field, 'ilike', search]);
    }

    return domain;
  }

  initSearch(): void {
    this.searchControl.valueChanges.pipe(
      debounceTime(1000),
      distinctUntilChanged()
    )
    .subscribe(value => {
      if(value != null){
        this.searchValue = value
        this.performSearch(this.searchValue)
      }
    });
    this.getDefaultSearchFields();
}

  clearSearchFilter() {
    this.selectedFilterField=''
    this.searchControl.reset()
    this.dateDomain=[]
    this.searchFilterPlaceholder = 'ALL'
    this.searchValue = ''

  }

  ionViewWillLeave() {
    this.destroyed$.next();
    this.destroyed$.complete();
    this.isScrolling = false
  }

  doFunction(functionName: OperationTypes, collectionId: number) {
    if (functionName in this)
      this[functionName](collectionId)
  }

  changeStatus(collectionId: number) {
    this.presentStatusAlert([collectionId]);
  }

  callSender(collectionId: number) {
    let model = 'rb_delivery.multi_print_orders_money_collector'
    if (this.collectionType=="pickup_request")
      model = 'rb_delivery.pickup_request'
   else if (this.collectionType!='collection')
      model = 'rb_delivery.returned_money_collection'
    this.odooRPC.searchRead(model, [['id', '=', collectionId]], ['business_whatsapp_mobile', 'business_second_whatsapp_mobile', 'mobile_number', 'second_business_mobile_number'], 0, 0, 'create_date desc').then(collection => {
      if (collection && collection.body && collection.body.result && collection.body.result.result && collection.body.result.result[0]) {
        collection = collection.body.result.result[0]
        
        let buttons:ActionSheetButton[]=[]
        if(collection.mobile_number || collection.second_business_mobile_number){
          buttons.push(
            {
              text:this.translate.instant('CALL_VIA_CELL'),
              handler:(()=>{
                this.callSenderViaCell(collection)
              }),
              icon:'call-outline'
            }
          )
        }
  
        if(collection.business_whatsapp_mobile || collection.business_second_whatsapp_mobile){
          buttons.push(
            {
              text:this.translate.instant('CALL_VIA_WHATSAPP'),
              handler:(()=>{
                this.callSenderViaWhatsapp(collection)
              }),
              icon:'logo-whatsapp'
            }
          )
        }
        let header = this.translate.instant('CALL')
        let errorMessage = this.translate.instant('NO_MOBILE_NUMBER_FOUND')
        this.showActionSheet(header,buttons,errorMessage)
      }
    })
  }

  showActionSheet(header: string, buttons: ActionSheetButton[],errorMessage:string) {
    if(buttons.length>1){
      this.actionSheetCtrl.create({
        mode:'md',
        header:header,
        buttons:buttons,
      }).then(actionSheet=>actionSheet.present())
    }
    else if(buttons.length==1 && buttons[0].handler){
      buttons[0].handler()
    }
    else{
      this.dialogService.error({
        header: header,
        message: errorMessage,
        code: '1205',
      })
    }
  }

  callSenderViaCell(collection: any) {
      let buttons:ActionSheetButton[]=[]
      if(collection.mobile_number){
        buttons.push(
          {
            text:collection.mobile_number,
            handler:(()=>{
              window.open("tel:"+collection.mobile_number+"", '_blank');
            }),
            icon:'call-outline'
          }
        )
      }
      if(collection.second_business_mobile_number){
        buttons.push(
          {
            text:collection.second_business_mobile_number,
            handler:(()=>{
              window.open("tel:"+collection.second_business_mobile_number+"", '_blank');
            }),
            icon:'call-outline'
          }
        )
      }
      let header = this.translate.instant('CALL')
      let errorMessage = this.translate.instant('NO_MOBILE_NUMBER_FOUND')
      this.showActionSheet(header,buttons,errorMessage)
  }

  callSenderViaWhatsapp(collection: any) {
      let buttons:ActionSheetButton[] = []
      if(collection.business_whatsapp_mobile){
        buttons.push(
          {
            text:collection.business_whatsapp_mobile,
            handler:(()=>{
              window.open("https://wa.me/"+collection.business_whatsapp_mobile, '_blank');
            }),
            icon:'logo-whatsapp'
          }
        )
      }
      if(collection.business_second_whatsapp_mobile){
        buttons.push(
          {
            text:collection.business_second_whatsapp_mobile,
            handler:(()=>{
              window.open("https://wa.me/"+collection.business_second_whatsapp_mobile, '_blank');
            }),
            icon:'logo-whatsapp'
          }
        )
      }
      let header = this.translate.instant('WHATSAPP_CALL')
      let errorMessage = this.translate.instant('NO_WHATSAPP_MOBILE_FOUND')
      this.showActionSheet(header,buttons,errorMessage)
  }

  messageSender(collectionId: number) {
    let model = 'rb_delivery.multi_print_orders_money_collector'
    if (this.collectionType!='collection')
      model = 'rb_delivery.returned_money_collection'
    this.odooRPC.searchRead(model, [['id', '=', collectionId]], ['business_whatsapp_mobile', 'business_second_whatsapp_mobile', 'mobile_number', 'second_business_mobile_number'], 0, 0, 'create_date desc').then(collection => {
      if (collection && collection.body && collection.body.result && collection.body.result.result && collection.body.result.result[0]) {
        collection = collection.body.result.result[0]
        
        let buttons:ActionSheetButton[] = []
        buttons.push(
          {
            text:this.translate.instant('MESSAGE_SENDER_VIA_WHATSAPP'),
            handler:(()=>{
              this.openWhatsAppMessageSheet(collection.business_whatsapp_mobile,collection.business_second_whatsapp_mobile,'')
            }),
            icon:'logo-whatsapp'
          }
        )
        buttons.push(
          {
            text:this.translate.instant('MESSAGE_SENDER_VIA_SMS'),
            handler:(()=>{
              this.openSmsMessageSheet(collection.mobile_number,collection.second_business_mobile_number,'')
            }),
            icon:'chatbubble-ellipses-outline'
          }
        )
        let header = this.translate.instant('MESSAGE_SENDER')
        let errorMessage = this.translate.instant('NO_MESSAGE_FOUND')
        this.showActionSheet(header,buttons,errorMessage)
      }
    })
  }

  openWhatsAppMessageSheet(firstWhatsappNumber:string,secondWhatsAppNumber:string,message:string){
    let buttons:ActionSheetButton[] = []
    if(firstWhatsappNumber){
      buttons.push(
        {
          text:firstWhatsappNumber,
          handler:(async ()=>{
            const url = "https://wa.me/" + firstWhatsappNumber + "?text=" + encodeURIComponent(message);
            await Browser.open({ url });
          }),
          icon:'logo-whatsapp'
        }
      )
    }
    if(secondWhatsAppNumber){
      buttons.push(
        {
          text:secondWhatsAppNumber,
          handler:(async ()=>{
            const url = "https://wa.me/" + secondWhatsAppNumber + "?text=" + encodeURIComponent(message);
            await Browser.open({ url });
          }),
          icon:'logo-whatsapp'
        }
      )
    }
    let header = this.translate.instant('WHATSAPP_MESSAGE')
    let errorMessage = this.translate.instant('NO_WHATSAPP_MOBILE_FOUND')
    this.showActionSheet(header,buttons,errorMessage)
  }

  openSmsMessageSheet(firstMobileNumber:string,secondMobileNumber:string,message:string){
    let buttons:ActionSheetButton[] = []
    if(firstMobileNumber){
      buttons.push(
        {
          text:firstMobileNumber,
          handler:(()=>{
            window.open("sms://"+firstMobileNumber+"/&body="+message, '_blank');
          }),
          icon:'chatbubble-ellipses-outline'
        }
      )
    }
    if(secondMobileNumber){
      buttons.push(
        {
          text:secondMobileNumber,
          handler:(()=>{
            window.open("sms://"+secondMobileNumber+"/&body="+message, '_blank');
          }),
          icon:'chatbubble-ellipses-outline'
        }
      )
    }
    let header = this.translate.instant('SMS_MESSAGE')
    let errorMessage = this.translate.instant('NO_SMS_MOBILE_FOUND')
    this.showActionSheet(header,buttons,errorMessage)
  }
  print(collectionId : number){
    this.printService.fetchReports(this.modelName,this.userInfo[0].group_id[0]).then(collectionReports =>{
      if(collectionReports && collectionReports.length > 0){
        this.displayReports(collectionReports,collectionId)
      }
      else{
        this.dialogService.error({
          input: this.translate.instant("NO_REPORTS_WHERE_FOUND"),
          message: this.translate.instant("NO_REPORTS_WHERE_FOUND"),
          code: '1602'
        })
      }
    })
  }

  displayReports(collectionReports : any,collectionId :number){
    let inputs = []
        for (let i = 0; i < collectionReports.length; i++) {
          let input = {
            type: "radio",
            name: collectionReports[i].name,
            value: collectionReports[i],
            label: this.translate.instant(collectionReports[i].name)
          }
          inputs.push(input);
        }
        const alertInputs: AlertInput[] = inputs as AlertInput[];
          this.alertCtrl.create({
            header: this.translate.instant('PRINT'),
            mode:'ios',
            inputs: alertInputs.map((input: any, index: number) => ({
              ...input,
                checked: index === 0
            })),
            buttons: [
              {
              text: this.translate.instant('CANCEL'),
              role: 'destructive',
              cssClass: 'secondary',
              handler: () => {
                console.log('Confirm Cancel');
              }
            },
            {
              text: this.translate.instant('YES'),
              handler: (report) => {
                this.printService.printReport(report.report_name,this.modelName,[collectionId],report.name)
              }
            }]
          }).then(alert => alert.present());
  }

  presentStatusAlert(collectionIds: number[], isBulk: boolean = false) {
    this.odooRPC.call("rb_delivery.status", 'get_collection_next_status', [collectionIds,this.collectionType,this.modelName])
      .then(async (statuses: any) => {
        if (statuses.body.result && statuses.body.result.success) {
          this.nextStatuses = statuses.body.result.result;

          const statusInputs = this.nextStatuses
            .filter((status: any[]) => status[0] !== undefined && status[1] !== undefined)
            .map((status: any[]) => ({
              name: 'status',
              type: 'radio',
              label: status[1],
              value: status[0],
            }));

          if (statusInputs.length < 1) {
            // Display a message indicating there are no valid statuses
            this.alertCtrl.create({
              header: this.translate.instant('WARNING'),
              mode: 'ios',
              message: this.translate.instant('NO_VALID_STATUSES'),
              buttons: [{
                text: this.translate.instant('OK'),
                role: 'cancel'
              }]
            }).then(alert => alert.present());
          }
          else {
            let statusAlert = await this.alertController.create({
              header: this.translate.instant('SELECT_STATUS'),
              mode: 'ios',
              inputs: statusInputs.map((input: any, index: number) => ({
                ...input,
                  checked: index === 0
              })),
            })
            let statusAlertButtons:(string | AlertButton)[] = [
              {
                text: this.translate.instant('CANCEL'),
                role: 'destructive',
                cssClass: 'secondary',
                handler: () => { },
              },
              {
                text: this.translate.instant('OK'),
                handler: async (selectedStatusId) => {
                    statusAlert.dismiss()
                    if (this.userInfo && this.userInfo[0] && this.userInfo[0].group_id && this.userInfo[0].group_id[0]) {
                      var group_id = this.userInfo[0].group_id[0];
                    }
                    else {
                      var group_id = undefined
                    }
                    let statusActionValues = await this.statusActionService.getStatusActionFields(selectedStatusId, group_id, this.collectionType,true);
                    if (!(typeof statusActionValues == "string")) {
                      statusActionValues['state'] = selectedStatusId;
                      return this.odooRPC.call('rb_delivery.utility', 'execute_status_actions', [statusActionValues, isBulk ? collectionIds : [collectionIds[0]], this.modelName]).then((executeStatusActionResponse: any) => {
                        if (executeStatusActionResponse && executeStatusActionResponse.body && executeStatusActionResponse.body.result && executeStatusActionResponse.body.result.success) {
                          if (isBulk) {
                            this.moneyCollectionStore.dispatch(new moneyCollectionActions.UpdateBulkHttp({ model: this.modelName, cardName: this.cardName, collectionsToUpdate: { ids: collectionIds } }));
                          } else {
                            this.moneyCollectionStore.dispatch(new moneyCollectionActions.UpdateHttp({ model: this.modelName, cardName: this.cardName, collection: { id: collectionIds[0] } }));
                          }
                        } else {
                          this.dialogService.warning({
                            input: executeStatusActionResponse.body.error.data.message,
                            message: executeStatusActionResponse.body.error.data.message,
                            code: '1601'
                          })
                        }
                      });
                    } else {
                      if (isBulk) {
                        this.moneyCollectionStore.dispatch(new moneyCollectionActions.UpdateBulkHttp({ model: this.modelName, cardName: this.cardName, collectionsToUpdate: { ids: collectionIds, values: { 'state': selectedStatusId } } }));
                      } else {
                        this.moneyCollectionStore.dispatch(new moneyCollectionActions.UpdateHttp({ model: this.modelName, cardName: this.cardName, collection: { id: collectionIds[0], state: selectedStatusId } }));
                      }
                    }
                  }
                 
                
              }
            ]
            statusAlert.buttons=statusAlertButtons
            statusAlert.present()
          }
        }
      })
      .catch(error => {
      });
  }

  goToCollectionOrders(collectionId:number){
    let domain :any[] = []
    switch(this.modelName){
      case 'rb_delivery.multi_print_orders_money_collector':
        domain = [['collection_id','=',collectionId]]
        break
      case 'rb_delivery.returned_money_collection':
        domain = [['returned_collection_id','=',collectionId]]
        break
      case 'rb_delivery.agent_money_collection':
        domain = [['agent_collection_id','=',collectionId]]
        break
      case 'rb_delivery.agent_returned_collection':
        domain = [['agent_returned_collection_id','=',collectionId]]
        break
      case 'rb_delivery.runsheet':
        domain = [['runsheet_collection_id','=',collectionId]]
        break
      case 'rb_delivery.pickup_request':
        domain = [['pickup_request_id','=',collectionId]]
        break
    } 
    let queryParams = {queryParams:{domain:JSON.stringify(domain)}}
    this.router.navigate(['/tabs/orders'],queryParams);
  }

  clearFilter(){
      this.router.navigate(['/tabs/collection'], {
        queryParams: {
          'collectionIds': [],
          'modelName':this.modelName,
          'cardName':this.cardName,
          'collectionType':this.collectionType 
        },
        queryParamsHandling: 'merge'
      })
  }

  openPickupForm(){
    this.modalCtrl.create({
      component: RecordCreatorPage,
      cssClass:"hide-when-scan-barcode",
      componentProps: {
        formName: 'pickup_request_form',
        field: {
          'name': this.translate.instant('PICKUP_REQUEST')
        }
      }
    }).then(modal => {
      modal.present()
    })
  }

  loadMoreCollection(event:any){
    this.infiniteScroll.next(event)
  }
}

enum OperationTypes {
  changeStatus = 'changeStatus'
}