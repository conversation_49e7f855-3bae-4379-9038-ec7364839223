import { Component, OnInit } from '@angular/core';
import { ActionSheetButton, ActionSheetController, AlertController, IonIcon, ModalController, NavController, ToastController } from '@ionic/angular';
import { RecordCreatorPage } from '../modals/record-creator/record-creator.page';
import * as resCompanyActions from 'src/app/ngrx-store/res-company/store/actions';
import { ResCompanyState } from '../ngrx-store/res-company/store';
import { Store } from '@ngrx/store';
import { TranslateService } from '@ngx-translate/core';
import { Storage } from '@ionic/storage-angular';
import { BarcodeScanComponent } from '../modals/barcode-scan/barcode-scan.component';
import { UpdateMultiOrdersComponent } from '../components/update-multi-orders/update-multi-orders.component';
import * as orderSelectors from 'src/app/ngrx-store/order/store/selectors';
import { OrderState, selectGroupedOrders } from 'src/app/ngrx-store/order/store';
import { StatusState } from '../ngrx-store/status/store';
import * as statusActions from 'src/app/ngrx-store/status/store/actions';
import * as statusSelector from 'src/app/ngrx-store/status/store/selectors';
import { Router } from '@angular/router';
import { UserState } from '../ngrx-store/user/store';
import * as userSelector from '../ngrx-store/user/store/selectors';
import * as userActions from '../ngrx-store/user/store/actions';
import { filter, take } from 'rxjs';
import { ClientConfigurationState } from '../ngrx-store/client-configuration/store';
import * as clientConfigurationsSelector from '../ngrx-store/client-configuration/store/selectors';
import { OdooJsonRPC } from '../services/odooJsonRPC';
import { ConfirmRequestMoneyCollectionComponent } from '../components/confirm-request-money-collection/confirm-request-money-collection.component';
import { TimerAlertComponent } from '../components/timer-alert/timer-alert.component';
import { dialogService } from '../services/error-handlar-service';
import { FollowerOrderScannerComponent } from '../components/follower-order-scan/follower-order-scanner.component';
import { FollowOrderService } from '../services/follow-orders-service';
import * as orderActions from 'src/app/ngrx-store/order/store/actions';
import { SortAndDestributeMenuComponent } from '../components/sort-and-destribute/sort-and-destribute-menu/sort-and-destribute-menu.component';
import { DriversRoundComponent } from '../components/drivers-round/drivers-round.component';

import { DynamicSelectionComponent } from '../components/dynamic-selection/dynamic-selection.component';
import { IconsMap } from '../assets-maps/icons-map';

interface OptionHandlers {
  requestCollection: () => ActionSheetButton | null;
  moneyCollection: () => ActionSheetButton;
  returnedCollection: () => ActionSheetButton;
  agentCollection: () => ActionSheetButton;
  agentReturnedCollection: () => ActionSheetButton;
  runsheetCollection: () => ActionSheetButton;
  pickupCollection: () => ActionSheetButton;
  tasks: () => ActionSheetButton;
}
@Component({
  selector: 'app-tabs',
  templateUrl: 'tabs.page.html',
  styleUrls: ['tabs.page.scss']
})


export class TabsPage implements OnInit{
  mainIconSrc: any = '';
  showMainButton: boolean = false;
  roleMainButton: string = '';
  statusesHavingAccess:any[]=[]
  userCanRequestCollection: boolean =false;
  userCollectionsOptions :any[]=[]
  showRequestbuttonForAll: boolean = false
  showNav!: boolean;
  navItems: any[]=[]
  userInfo : any

  constructor(
    private orderStore: Store<OrderState>,
    private modalControl: ModalController,
    private actionSheetCtrl: ActionSheetController,
    private navCtrl: NavController,
    private resCompany : Store<ResCompanyState>,
    private translate: TranslateService,
    private statusStore : Store<StatusState>,
    private router : Router,
    private storage : Storage,
    private userStore : Store<UserState>,
    private clientConfigurationStore: Store<ClientConfigurationState>,
    private alertCtrl : AlertController,
    private odooJsonRPC : OdooJsonRPC,
    private toastCtrl : ToastController,
    private modalCtrl : ModalController,
    private dialogService: dialogService,
    private followOrdersService: FollowOrderService

  ) { 
    this.resCompany.dispatch(new resCompanyActions.LoadHttp({ offset: 0 }));
  }
  ngOnInit(): void {
    this.storage.get('user_info').then(async userInfo=>{
      this.getCurrentUser(userInfo)
      this.checkUserMainButton(userInfo)
      this.checkUserCollectionOptions(userInfo)
      this.getClientConfiguration(userInfo)
      this.checkNavigationBarConfig(userInfo)
    })
    
  }
  checkNavigationBarConfig(userInfo: any) {
    this.showNav=!('show_nav' in userInfo[0]) || userInfo[0].show_nav
    this.navItems = 'nav_items' in userInfo[0] ? userInfo[0]['nav_items'] :[]
  }

  getClientConfiguration(userInfo : any){
    this.clientConfigurationStore.select(clientConfigurationsSelector.selectClientConfigurationByKeys(['enable_request_collection_for_all_businesses'])).pipe(filter((keys) => keys && keys.length > 0), take(1)).subscribe(configurationKeys => {
      if (configurationKeys && configurationKeys.length > 0) {
        for (let configurationKey of configurationKeys) {
          if (configurationKey.value) {
            this.showRequestbuttonForAll = true
          }
        }
        
      }
    })
  }


  async checkUserCollectionOptions(userInfo:any){
    let userCollectionsOptionsResponse = await this.odooJsonRPC.call('rb_delivery.mobile_collection_sheet_creator','get_collection_items',[])
    if(userCollectionsOptionsResponse && userCollectionsOptionsResponse.body && userCollectionsOptionsResponse.body.result &&  userCollectionsOptionsResponse.body.result.success){
      this.userCollectionsOptions = userCollectionsOptionsResponse.body.result.result
    }
  }

  async checkUserMainButton(userInfo:any){
    let domain = [['group_id','=',userInfo[0].group_id[0]]];
    let mainButtonResponse = await this.odooJsonRPC.searchRead('rb_delivery.create_order_button_creator',domain,['action_id','button_icon'],0,0,'id ASC')
    if(mainButtonResponse && mainButtonResponse.body && mainButtonResponse.body.result &&  mainButtonResponse.body.result.success){
      let roleMainButton = mainButtonResponse.body.result.result
      if(roleMainButton.length >0){
        roleMainButton = roleMainButton[0]
        if(roleMainButton.action_id && roleMainButton.action_id.length >0){
          if (roleMainButton.action_id[1] == 'createOrder'){
            this.showMainButton = true
            this.mainIconSrc = roleMainButton.button_icon ? roleMainButton.button_icon : 'createOrder'
            this.roleMainButton = 'createOrder'
          }
          else if (roleMainButton.action_id[1] == 'scanOrder'){
            this.showMainButton = true
            this.mainIconSrc = roleMainButton.button_icon ? roleMainButton.button_icon : 'scanOrder'
            this.roleMainButton = 'scanOrder'
            this.loadStatuses(userInfo)
          }
          else{
            this.showMainButton = true
          }
        }
        else{
          this.showMainButton = true
        }
      }
      else{
        this.showMainButton = true
      }
    }
  }

  checkInIconMap(iconName: keyof typeof IconsMap) {
    if (iconName in IconsMap) {
      return {
        exist: true,
        path: IconsMap[iconName as keyof typeof IconsMap],
      };
    } else {
      return {
        exist: false,
      };
    }
  }

  getCurrentUser(userInfo:any){
      this.userStore.dispatch(new userActions.LoadHttp({search:[['id','=',userInfo[0].id]],fields:['show_request_collection_button','online']}))
      this.userStore.select(userSelector.selectUserById(userInfo[0].id)).pipe(filter(data => data && Object.keys(data).length > 0),take(1)).subscribe(userData=>{
        this.storage.set('user_info',[{...userInfo[0],'online':userData.online}])
        if('button_style' in userInfo[0] && userInfo[0]['button_style'] && userInfo[0]['button_style']=='rounded'){
          document.documentElement.style.setProperty('--button-radius', '24pt')
          document.documentElement.style.setProperty('--button-shadow', 'var(--ion-color-primary) 0px 5px 20px -8px')
        }
        this.userCanRequestCollection = userData['show_request_collection_button']
      })
  }
  
  loadStatuses(userInfo:any){
    this.statusStore.dispatch(new statusActions.LoadHttp({search:[['status_type','=','olivery_order']]}))
    this.statusStore.select(statusSelector.selectHavingAccessStatus(userInfo[0].group_id[0])).pipe(filter(statuses=>statuses.length>0),take(1)).subscribe(statuses=>{
      this.statusesHavingAccess=statuses
    })
  }

  checkRoleMainButton(){
    switch(this.roleMainButton){
      case 'createOrder':
        this.openOrderModal()
        break
      case 'scanOrder':
        this.showScanOptions()
        break
      case '':
        break
      default:
        break
    }
  }

  showScanOptions(){
    let buttons = [
      {
        text:this.translate.instant('SEARCH_ON_ORDER'),
        handler:(()=>{
          this.startScanning('searchOrder')
        }),
        icon:'search'
      },
      {
        text:this.translate.instant('SORT_AND_DESTRIBUTE'),
        handler:(()=>{
          this.sortAndDestribute()
        }),
        icon:'search'
      },
      {
        text:this.translate.instant('CHANGE_STATUS'),
        handler:(()=>{
          this.startScanning('changeStatus')
        }),
        icon:'create'
      },
      {
        text: this.translate.instant('CANCEL'),
        role: 'cancel',
        icon: 'close'
      },
    ]
    buttons.splice(1,0,{
      text:this.translate.instant('OPEN_ROUND'),
      handler:(()=>{
        this.openDriverRound()
      }),
      icon:'search'
    },)
    let header = this.translate.instant('BARCODE_ACTIONS')
    let errorMessage = this.translate.instant('NO_BARCODE_ACTIONS_FOUND')
    this.showActionSheet(header,buttons,errorMessage)
  }


  async sortAndDestribute() {
    let prevent_change = false
    let available = await this.odooJsonRPC.call('rb_delivery.mobile_sort_and_distribute', 'get_if_should_scan_follow_orders', []).then((result) => {
      if (result && result.body && result.body.result && result.body.result.result) {
        prevent_change = result.body.result.result
        return true
      } else if (result && result.body.error) {
        this.dialogService.error({
          message: 'THIS_FEATURE_IS_NOT_AVAILABLE',
          whatToDo: 'PLEASE_CONTACT_SUPPORT_TO_ACTIVATE_THIS_FEATURE',
          title: 'FEATUE_NOT_AVAILABLE',
          code: '1299'
        })
        return false
      }
      return true
    })

    if (available) {
    this.modalCtrl.create({
      component: SortAndDestributeMenuComponent,
      backdropDismiss:false,
        componentProps: {
          prevent_change: prevent_change
        },
      cssClass: 'sort-and-destribute'
    }).then(modal => {
      modal.present();
    })
    }
    
  }

  async openDriverRound() {
    let prevent_change = false
    let available = await this.odooJsonRPC.call('olivery_accounting.driver_round', 'open_round_mobile', [[]]).then((result) => {
      if (result && result.body && result.body.result && result.body.result.result) {
        prevent_change = result.body.result.result
        return true
      } else if (result && result.body.error) {
        this.dialogService.error({
          message: 'THIS_FEATURE_IS_NOT_AVAILABLE',
          whatToDo: 'PLEASE_CONTACT_SUPPORT_TO_ACTIVATE_THIS_FEATURE',
          title: 'FEATUE_NOT_AVAILABLE',
          code: '1298'
        })
        return false
      }
      return true
    })

    if (available) {
    this.modalCtrl.create({
      component: DriversRoundComponent,
      backdropDismiss:false,
        componentProps: {
          prevent_change: prevent_change
        },
      cssClass: 'sort-and-destribute'
    }).then(modal => {
      modal.present();
    })
    }
    
  }

  showActionSheet(header: string, buttons: ActionSheetButton[],errorMessage:string) {
    if(buttons.length>1){
      this.actionSheetCtrl.create({
        mode:'md',
        header:header,
        buttons:buttons,
      }).then(actionSheet=>actionSheet.present())
    }
    else if(buttons.length==1 && buttons[0].handler){
      buttons[0].handler()
    }
    else{
      this.dialogService.warning({
        input: errorMessage,
        message: errorMessage,
        title : header
      })
    }
  }

  async startScanning(action:string){
    if(action == 'searchOrder'){
      this.openBarcodeScanner().then(scanResult=>{
        if(scanResult.data.scanningFinished){
          return
        }
        if(scanResult.data){
          let sequence = scanResult.data
          this.router.navigate(['/tabs/orders'],{queryParams: {sequence:JSON.stringify(sequence)}}) 
        }
      })
    }
    else if (action == 'changeStatus'){
      this.checkStatusHavingAccess()
    }
  }
  openBarcodeScanner(componentProps={}): Promise<any> {
    // this.scanActivate = true;
    return new Promise((resolve, reject) => {
      this.modalCtrl.create({
        component: BarcodeScanComponent,
        backdropDismiss:false,
        componentProps,
        cssClass:"exclude-hide"
      }).then(modal => {
        modal.present();
        modal.onDidDismiss().then(scanResult => {
          // this.scanActivate = false;
          resolve(scanResult);
        })
      })
    });
  }

  checkStatusHavingAccess(){
    let buttons = []
    for (let state of this.statusesHavingAccess){
      buttons.push({
        text:state.title,
      handler:(()=>{
        this.openBarcodeListModel(state)
      }),
      })
    }
    buttons.push({
      text: this.translate.instant('CANCEL'),
      role: 'cancel',
      icon: 'close'
    })
    let header = this.translate.instant('STATUS')
    let error = this.translate.instant('THER_IS_NO_STATUSES')
    this.showActionSheet(header,buttons,error)
  }

  async openBarcodeListModel(state : any){

    let goIntoFollowOrdersScan = await this.followOrdersService.validateIfShouldScanFollowOrders(state.name)

    this.openBarcodeScanner({typeSelectionItems:['SEQUENCE','REFERENCE']}).then(scanResult=>{
      if(scanResult.data.scanningFinished){
        return
      }
      if (scanResult.data) {
        (async () => {
          console.log(scanResult)
          let type_ref = false
          if (scanResult.data.type == 'REFERENCE')
            type_ref = true
          var follower_obj = await this.followOrdersService.getFollowSequences(scanResult.data.scannedValue);
          if(follower_obj && follower_obj.length > 0 && goIntoFollowOrdersScan){
            const names = follower_obj.map((item: { name: string }) => item.name);
            const barcodes = follower_obj.map((item: { follow_up_sequence: string }) => item.follow_up_sequence);
            this.modalCtrl.create({
              component : FollowerOrderScannerComponent,
              cssClass:"hide-when-scan-barcode",
              componentProps :{
                state : state,
                originalSequence : scanResult.data.scannedValue,
                sequenceBarcodeList : barcodes,
                nameList: names,
                is_reference: type_ref,
                change:false
              }}).then(modal =>{
                modal.present()

                modal.onDidDismiss().then(()=>{
                  this.modalCtrl.create({
                    component : UpdateMultiOrdersComponent,
                    cssClass:"hide-when-scan-barcode",
                    componentProps :{
                      state : state,
                      barcodeList : [scanResult.data.scannedValue],
                      firstScanType : scanResult.data.type
                    }}).then(modal =>{
                      modal.present()
                      modal.onDidDismiss().then(()=>{
                        this.orderStore.dispatch(new orderActions.LoadHttp({offset: 0}))
                      })
                    })
                })
              })
          } else {
            this.modalCtrl.create({
              component : UpdateMultiOrdersComponent,
              cssClass:"hide-when-scan-barcode",
              componentProps :{
                state : state,
                barcodeList : [scanResult.data.scannedValue],
                firstScanType : scanResult.data.type
              }}).then(modal =>{
                modal.present()
                modal.onDidDismiss().then(()=>{
                  this.orderStore.dispatch(new orderActions.LoadHttp({offset: 0}))
                })
              })
          }
        })();
        }
    })
  }

  openOrderModal() {
    this.modalControl.create({
      component: RecordCreatorPage,
      cssClass:"hide-when-scan-barcode",
      componentProps: {
        formName: 'order_form',
        field: {
          'name': this.translate.instant('ORDER')
        }
      }
    }).then(modal => {
      modal.present()
    })
  }

  
  isFormRedirect(index: number): boolean {
    return this.navItems && this.navItems.length > index && this.navItems[index].redirect_type !== 'to_collection';
  }
  
  isCollectionRedirect(index: number): boolean {
    return this.navItems && this.navItems.length > index && this.navItems[index].redirect_type === 'to_collection';
  }


  async openCollectionSelector() {
    let actionSheetButtons: ActionSheetButton[] = [];

    const optionHandlers: Record<string, () => ActionSheetButton | null> = {
      requestCollection: () => {
        if (this.userCanRequestCollection || this.showRequestbuttonForAll) {
          return {
            text: this.translate.instant('REQUEST_COLLECTION'),
            handler: () => this.showConfirmationRequestCollection(),
            icon: "../../../../assets/icon/request_collection.svg",
            cssClass: 'request-collection-button',
          };
        }
        return null;
      },
      requestAgentCollection: () => {
        return {
          text: this.translate.instant('REQUEST_AGENT_COLLECTION'),
          handler: () => this.showConfirmationRequestAgentCollection(),
          icon: "../../../../assets/icon/request_collection.svg",
          cssClass: 'request-collection-button',
        };
    },
      moneyCollection: () => ({
        text: this.translate.instant('COLLECTION'),
        handler: () => this.navigateToCollectionPage('rb_delivery.multi_print_orders_money_collector', 'money_collection_card', 'collection'),
        icon: "cash-outline",
      }),
      returnedCollection: () => ({
        text: this.translate.instant('RETURNED_COLLECTIONS'),
        handler: () => this.navigateToCollectionPage('rb_delivery.returned_money_collection', 'returned_money_collection_card', 'returned_collection'),
        icon: "../../../../assets/icon/returned_collection.svg",
      }),
      agentCollection: () => ({
        text: this.translate.instant('AGENT_COLLECTION'),
        handler: () => this.navigateToCollectionPage('rb_delivery.agent_money_collection', 'agent_money_collection_card', 'agent_collection'),
        icon: "../../../../assets/icon/agent_collection.svg",
      }),
      agentReturnedCollection: () => ({
        text: this.translate.instant('AGENT_RETURNED_COLLECTION'),
        handler: () => this.navigateToCollectionPage('rb_delivery.agent_returned_collection', 'agent_returned_collection_card', 'agent_returned_collection'),
        icon: "../../../../assets/icon/agent_returned_collection.svg",
      }),
      runsheetCollection: () => ({
        text: this.translate.instant('RUN_SHEET'),
        handler: () => this.navigateToCollectionPage('rb_delivery.runsheet', 'runsheet_card', 'runsheet_collection'),
        icon: "../../../../assets/icon/runsheet.svg",
      }),
      pickupCollection: () => ({
        text: this.translate.instant('PICKUP_REQUESTS'),
        handler: () => this.navigateToCollectionPage('rb_delivery.pickup_request', 'pickup_request_card', 'pickup_request'),
        icon: "../../../../assets/icon/pickup_request.svg",
      }),
      tasks: () => ({
        text: this.translate.instant('TASKS'),
        handler: () => this.navigateToTasksPage(),
        icon: "../../../../assets/icon/tasks.svg",
      })
    };

    this.userCollectionsOptions.forEach(userOption => {
      const option = optionHandlers[userOption.name]?.() as ActionSheetButton | null;
      if (option) {
        if (userOption.name === 'requestCollection' || userOption.name === 'requestAgentCollection') {
          actionSheetButtons.unshift(option);
        } else {
          actionSheetButtons.push(option);
        }
      }
    });

    this.openCollectionSheet(actionSheetButtons);
  }

  async openCollectionSheet(actionSheetButtons:any){
    const actionSheet = this.actionSheetCtrl.create({
      buttons:actionSheetButtons,
      backdropDismiss: true,
      animated: true,
      keyboardClose: true,
      mode: 'ios',
    });

    (await actionSheet).present();
  }

  public navigateToCollectionPage(modelName: string,cardName:string,collectionType:string) {
    if (modelName) {
      this.router.navigate(['tabs/collection'], {
        queryParams: { modelName,cardName,collectionType }
      })
    }
  }

  public navigateToTasksPage() {
      this.router.navigate(['tabs/tasks'])
  }

  showConfirmationRequestCollection(){
    this.odooJsonRPC.call('rb_delivery.order','request_money_collection',[]).then(ordersResponse =>{
      if(ordersResponse && ordersResponse.body && ordersResponse.body.result && ordersResponse.body.result.success){
        let ordersCount = ordersResponse.body.result.result.order_count
        let ordersCash = ordersResponse.body.result.result.required_from_business
        this.modalCtrl.create({
          component: ConfirmRequestMoneyCollectionComponent,
          cssClass:'request-collection-modal',
          mode:'ios',
          componentProps: {
            ordersCount:ordersCount,
            ordersCash : ordersCash,
            collectionType:'Collection'
          }
        }).then(modal =>{
           modal.present()
          })
      }
      else{
        this.dialogService.warning({
          input: ordersResponse.body.error.data.message,
          message: ordersResponse.body.error.data.message,
          title : this.translate.instant('REQUEST_COLLECTION'),
          code : '1400'
        })
      }
    })
  }
  async showConfirmationRequestAgentCollection(){
    try {
      this.clientConfigurationStore.select(clientConfigurationsSelector.selectClientConfigurationByKeys(['should_spicify_business_when_request_agent_collection'])).pipe(take(1)).subscribe(async configurationKeys => {
        let args:any = []
        if(configurationKeys && configurationKeys[0] && configurationKeys[0].value){

          const businessResponse = await this.odooJsonRPC.call('rb_delivery.order', 'get_agent_collection_orders', []);
          if (businessResponse && businessResponse.body && businessResponse.body.result && businessResponse.body.result.success) {
            const businesses = businessResponse.body.result.result;
    
            const businessOptions = businesses.map((item: any) => item.assign_to_business);
            const uniqueBusinessOptions: Array<[number, string]> = businessOptions.filter((biz: any[], idx: any, arr: [any][]) =>
              arr.findIndex(([id]) => id === biz[0]) === idx
            );        
            console.log('Business options:', uniqueBusinessOptions);
            let selectedBusinessId = 0;

    
            if (uniqueBusinessOptions.length > 1) {
              const businesses = uniqueBusinessOptions.map(([id, display_name]) => ({ id, display_name }));

              const modal = await this.modalCtrl.create({
                component: DynamicSelectionComponent,
                initialBreakpoint : 0.65,
                breakpoints :[0, 0.25, 0.5, 0.75,1],
                handleBehavior : 'cycle',
                componentProps: {
                  modelName: 'rb_delivery.user',
                  selectionItems: businesses,
                  placeholder:'BUSINESS',
                  fieldType: 'many2one',
                  selectionType:'many2one',
                  limitPerSearch: 10
                }
              });

              await modal.present();
              const result = await modal.onDidDismiss();

              if (result.data && result.data.id) {
                selectedBusinessId = result.data.id;
                args.push(selectedBusinessId);
              }
              
    
            } else if (uniqueBusinessOptions.length === 1) {
              selectedBusinessId = uniqueBusinessOptions[0][0];
            } else {
              this.dialogService.warning({
                input: this.translate.instant('NO_BUSINESSES_FOUND'),
                message: this.translate.instant('NO_BUSINESSES_FOUND'),
                title: this.translate.instant('REQUEST_COLLECTION'),
                code: '1400'
              });
              return;
            }
          } else {
            this.dialogService.warning({
              input: businessResponse.body.error.data.message,
              message: businessResponse.body.error.data.message,
              title: this.translate.instant('REQUEST_COLLECTION'),
              code: '1400'
            });
          }
        }

        const ordersResponse = await this.odooJsonRPC.call('rb_delivery.order', 'request_agent_money_collection', args);
    
        if (ordersResponse && ordersResponse.body && ordersResponse.body.result && ordersResponse.body.result.success) {
          const ordersCount = ordersResponse.body.result.result.order_count;
          const ordersCash = ordersResponse.body.result.result.customer_payments;
          const stuckCount = ordersResponse.body.result.result.stuck_orders_count;
          const modal = await this.modalCtrl.create({
            component: ConfirmRequestMoneyCollectionComponent,
            cssClass: 'request-agent-collection-modal',
            mode: 'ios',
            componentProps: {
              ordersCount: ordersCount,
              ordersCash: ordersCash,
              stuckCount: stuckCount,
              collectionType: 'agentCollection',
              businessId: args && args.length > 0 ? args[0] : 0
            }
          });

          await modal.present();
        } else {
          this.dialogService.warning({
            input: ordersResponse.body.error.data.message,
            message: ordersResponse.body.error.data.message,
            title: this.translate.instant('REQUEST_COLLECTION'),
            code: '1400'
          });
        }
      })    

    } catch (error) {
      console.error('Error in showConfirmationRequestAgentCollection:', error);
      this.dialogService.error({
        input: this.translate.instant('SOMETHING_WENT_WRONG'),
        message: this.translate.instant('SOMETHING_WENT_WRONG'),
        title: this.translate.instant('REQUEST_COLLECTION'),
        code: '1400'
      });
    }
  }

}
