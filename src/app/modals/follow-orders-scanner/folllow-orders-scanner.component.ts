import { Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { ModalController, Platform } from '@ionic/angular';
import { Camera } from '@capacitor/camera';
import { Haptics, ImpactStyle } from '@capacitor/haptics';
import { Html5Qrcode, Html5QrcodeCameraScanConfig, Html5QrcodeFullConfig, Html5QrcodeResult, Html5QrcodeSupportedFormats } from 'html5-qrcode';
import { Storage } from '@ionic/storage-angular';
import BarcodeScanner from 'barcode-scanner';
import { OdooJsonRPC } from 'src/app/services/odooJsonRPC';
import { ViewDidEnter } from '@ionic/angular';
import { captureFrame, drawBoundingBox, getCameraStream } from '../barcode-scan/camera-frame-utils';

@Component({
  selector: 'folllow-orders-scanner',
  templateUrl: './folllow-orders-scanner.component.html',
  styleUrls: ['./folllow-orders-scanner.component.scss'],
})

export class FollowOrdersScanner  implements OnInit, ViewDidEnter {
  @Input({required:true}) mode!: 'single' | 'multi';
  @ViewChild('reader') reader!: ElementRef | undefined;
  @ViewChild('barcodeList', { static: false }) barcodeList!: ElementRef;
  barcodes: string[] = [];
  html5QrcodeScanner: Html5Qrcode | undefined;
  @Input() showCompleteWithoutBarcodeButton: boolean = false;
  flash: boolean = false;
  userInfo: any;
  selectedType: any;
  scannerLoading: boolean = true;
  isScanning: boolean = false;
  @Input() modeName: 'barcode' | 'qrcode' = 'barcode';
  latestResult: string | null = null;

  typeSelectionItems: string[] = [];

  videoElement?: HTMLVideoElement;
  canvasElement?: HTMLCanvasElement;
  frameInterval?: any;
  scanRegionBox?: { x: number; y: number; width: number; height: number };


  constructor(
    private modalCtrl: ModalController,
    private platform: Platform,
    private odooRpc: OdooJsonRPC,
    private storage: Storage,
  ) {}

  ngAfterViewInit(): void {}

  ngOnInit() {}

  ionViewDidEnter() {
    if (this.typeSelectionItems.length > 0 && !this.selectedType) {
      this.selectedType = this.typeSelectionItems[0];
    }
    this.getUserInfo();
    this.startScanner();
    this.setupCameraElements();
  }

  async setupCameraElements() {
    const container = document.getElementById('scanner-container');
    if (!container) return;
    // Remove previous video/canvas if any
    if (this.videoElement && this.videoElement.parentElement) {
      this.videoElement.parentElement.removeChild(this.videoElement);
    }
    if (this.canvasElement && this.canvasElement.parentElement) {
      this.canvasElement.parentElement.removeChild(this.canvasElement);
    }
    // Container styling
    container.style.position = 'relative';
    container.style.width = '100%';
    container.style.height = '100%';
    container.style.maxWidth = '100vw';
    container.style.maxHeight = '100vh';

    // Create video element (fills container, no inversion)
    this.videoElement = document.createElement('video');
    this.videoElement.setAttribute('playsinline', 'true');
    this.videoElement.style.width = '100%';
    this.videoElement.style.height = '100%';
    this.videoElement.style.objectFit = 'cover';
    this.videoElement.style.position = 'absolute';
    this.videoElement.style.top = '0';
    this.videoElement.style.left = '0';
    this.videoElement.style.zIndex = '1';
    // Remove any inversion
    this.videoElement.style.transform = '';
    container.appendChild(this.videoElement);

    // Canvas sizing logic
    let canvasWidth, canvasHeight;
    if (this.modeName === 'barcode') {
      // Rectangle: wide and short
      canvasWidth = container.offsetWidth * 0.8;
      canvasHeight = container.offsetHeight * 0.25;
    } else {
      // Square: centered
      const minDim = Math.min(container.offsetWidth, container.offsetHeight);
      canvasWidth = minDim * 0.6;
      canvasHeight = minDim * 0.6;
    }

    // Create canvas (centered, visible border)
    let canvasElement = document.getElementById('scan-region') as HTMLCanvasElement
    canvasElement.style.width = '95%'
    canvasElement.style.height = '20%';
    canvasElement.style.position = 'absolute';
    canvasElement.style.top = '40%';
    canvasElement.style.left = '2.5%';
    canvasElement.style.border = '2px solid rgba(0, 255, 0, 0.5)';
    canvasElement.style.borderRadius = '10px';
    canvasElement.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
    canvasElement.style.pointerEvents = 'none'; // Allow clicks to pass
    this.canvasElement = canvasElement;

    container.appendChild(canvasElement);
  }

  getUserInfo() {
    this.storage.get('user_info').then((userInfo) => {
      if (userInfo) {
        this.userInfo = userInfo;
      }
    });
  }

  playSound() {
    const audio = new Audio('/assets/audio/beep.mp3');
    audio.play();
  }

  triggerVibration() {
    Haptics.impact({
      style: ImpactStyle.Heavy,
    });
  }

  async selectHighestResolutionCamera(): Promise<string | null> {
    try {
      const cameras = await Html5Qrcode.getCameras();
      if (cameras && cameras.length) {
        let selectedCamera = cameras[0];
        cameras.forEach((camera) => {
          if (camera.label && camera.label.toLowerCase().includes('ultra')) {
            selectedCamera = camera;
          }
        });
        console.log('Selected Camera:', selectedCamera);
        return selectedCamera.id;
      }
    } catch (err) {
      console.error('Error getting cameras:', err);
    }
    return null;
  }

  async startScanner() {
    if (!this.videoElement || !this.canvasElement) {
      await this.setupCameraElements();
    }
    try {
      const stream = await getCameraStream(this.videoElement!);
      this.videoElement!.srcObject = stream;
      await this.videoElement!.play();
      this.isScanning = true;
      this.scannerLoading = false;
      this.startFrameCapture();
    } catch (err) {
      console.error('Camera error:', err);
      this.scannerLoading = false;
    }
  }

  startFrameCapture() {
    if (this.frameInterval) clearInterval(this.frameInterval);
    this.frameInterval = setInterval(() => this.processFrame(), 300);
  }

  async processFrame() {
    if (!this.videoElement || !this.canvasElement || !this.isScanning) return;
    const base64 = captureFrame(this.videoElement, this.canvasElement, this.modeName);
    if (!base64) return;
    try {
      const scanResult = await BarcodeScanner.scanFromImage({ base64, path: base64 });
      if (scanResult.hasContent && scanResult.content) {
        this.latestResult = scanResult.content;
        if (scanResult.boundingBox) {
          this.scanRegionBox = scanResult.boundingBox;
          drawBoundingBox(this.canvasElement, scanResult.boundingBox);
        }
        if (!this.barcodes.includes(scanResult.content)) {
          this.barcodes.push(scanResult.content);
          if (this.mode === 'single') {
            this.stopScanner();
          } else {
            this.triggerVibration();
            this.triggerFlashEffect();
          }
        }
      } else {
        this.scanRegionBox = undefined;
        this.clearCanvas();
      }
    } catch (err) {
      // Ignore frame errors
    }
  }

  clearCanvas() {
    if (!this.canvasElement) return;
    const ctx = this.canvasElement.getContext('2d');
    if (ctx) ctx.clearRect(0, 0, this.canvasElement.width, this.canvasElement.height);
  }

  onScannFail(error: string) {
    console.error(error)
  }

  async stopScanner() {
    if (this.html5QrcodeScanner) {
      try {
        await this.html5QrcodeScanner.stop();
        this.html5QrcodeScanner.clear();
        if (!this.barcodes.length) {
          this.modalCtrl.dismiss({scanningFinished:true})
        }

        this.modalCtrl.dismiss(this.barcodes[0])
      } catch (err) {
        this.modalCtrl.dismiss({scanningFinished:true})
        console.error('Error stopping scanner:', err);
      } finally {
        this.html5QrcodeScanner = undefined;
        this.reader = undefined
      }
    }
    else{
      this.modalCtrl.dismiss(this.barcodes[0])
    }
  }
  triggerFlashEffect() {
    this.flash = true;
    setTimeout(() => {
      this.flash = false;
    }, 300);
  }

  async completeWithoutBarcode() {
    if (this.html5QrcodeScanner) {
      try {
        await this.html5QrcodeScanner.stop();
        this.html5QrcodeScanner.clear();
        this.modalCtrl.dismiss('complete');
      } catch (err) {
        console.error('Error completing without barcode:', err);
      }
    }
  }


  async changeMode(event: any) {
    const newMode = event && event.detail && event.detail.value ? event.detail.value : this.modeName;
    if (newMode === this.modeName) return; // No change
    this.modeName = newMode;
    // Update canvas style
    if (this.canvasElement) {
      if (this.modeName === 'barcode') {
        this.canvasElement.style.width = '95%';
        this.canvasElement.style.height = '20%';
        this.canvasElement.style.top = '40%';
        this.canvasElement.style.left = '2.5%';
      } else if (this.modeName === 'qrcode') {
        this.canvasElement.style.width = '80%';
        this.canvasElement.style.height = '80%';
        this.canvasElement.style.top = '10%';
        this.canvasElement.style.left = '10%';
      }
    }
    // Restart scanner if scanning
    if (this.isScanning) {
      await this.stopScanner();
      this.startScanner();
    }
  }

  getImageSrc() {
    return this.modeName === 'barcode' ? '../../../assets/icon/qr_icon.svg' : '../../../assets/icon/barcode_icon.svg';
  }

  getTextSrc() {
    return this.modeName === 'barcode' ? 'qrcode' : 'barcode';
  }
}