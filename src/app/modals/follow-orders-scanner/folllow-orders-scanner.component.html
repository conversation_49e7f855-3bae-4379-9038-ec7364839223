<ion-content class="ion-padding">
  <div class="scanner-container" style="position:relative;">
    <div  [ngClass]="{ 'scan-overlay.barcode': modeName === 'barcode', 'scan-overlay.qrcode': modeName === 'qrcode' }"></div>

    <div id="scanner-container" class="video-container" [ngClass]="modeName"></div>

    <div class="scan-overlay" [ngClass]="modeName">
      <canvas id="scan-region" class="scan-region"></canvas>
    </div>
    
    <div class="result-container" *ngIf="latestResult">
      <div class="result-box">
        <h3>{{ 'SCANNED_CODE' | translate }}</h3>
        <p class="result-value">{{ latestResult }}</p>
      </div>
    </div>
  </div>

  <div class="controls">
    <ion-segment [(ngModel)]="modeName" (ionChange)="changeMode($event)">
      <ion-segment-button value="barcode">
        <ion-label>{{ 'BARCODE' | translate }}</ion-label>
      </ion-segment-button>
      <ion-segment-button value="qrcode">
        <ion-label>{{ 'QRCODE' | translate }}</ion-label>
      </ion-segment-button>
    </ion-segment>
    
    <ion-button expand="block" (click)="isScanning ? stopScanner() : startScanner()" [color]="isScanning ? 'danger' : 'primary'">
        {{ isScanning ? ('STOP_SCAN' | translate) : ('START_SCAN' | translate) }}
    </ion-button>
  
  </div>
</ion-content>
