import { Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { ModalController } from '@ionic/angular';
import { Haptics, ImpactStyle } from '@capacitor/haptics';
import BarcodeScanner from 'barcode-scanner';
import { getCameraStream, captureFrame, drawBoundingBox } from './camera-frame-utils';
import { Storage } from '@ionic/storage-angular';
import { OdooJsonRPC } from 'src/app/services/odooJsonRPC';
import { ClientConfigurationState } from '../../ngrx-store/client-configuration/store/state';
import * as clientConfigurationsSelector from '../../ngrx-store/client-configuration/store/selectors';
import { Store } from '@ngrx/store';
import { filter, Subject, take, takeUntil } from 'rxjs';
import { RecordCardStructure } from 'src/app/services/record-structure-service';
import { RecordItemComponent } from 'src/app/components/record-item/record-item.component';
import { RecordStructureService } from 'src/app/services/record-structure-service';
import { ViewDidEnter } from '@ionic/angular';


@Component({
  selector: 'app-barcode-scan',
  templateUrl: './barcode-scan.component.html',
  styleUrls: ['./barcode-scan.component.scss'],
})
export class BarcodeScanComponent implements OnInit, ViewDidEnter {
  @Input() mode: 'single' | 'multi' = 'single';
  @ViewChild('reader') reader!: ElementRef | undefined;
  @ViewChild('barcodeList', { static: false }) barcodeList!: ElementRef;
  barcodes: string[] = [];
  @Input() showCompleteWithoutBarcodeButton: boolean = false;
  flash: boolean = false;
  userInfo: any;
  selectedType: any;
  scannerLoading: boolean = true;
  isScanning: boolean = false;
  @Input() modeName: 'barcode' | 'qrcode' = 'barcode';
  activeModal:any
  latestResult: string | null = null;

  typeSelectionItems: string[] = [];

  orders:{ [index: string]: any | false } = {};

  videoElement?: HTMLVideoElement;
  canvasElement?: HTMLCanvasElement;
  frameInterval?: any;
  scanRegionBox?: { x: number; y: number; width: number; height: number };

  constructor(
    private modalCtrl: ModalController,
    private odooRpc: OdooJsonRPC,
    private storage: Storage,
    private clientConfigurationStore: Store<ClientConfigurationState>,
    private recordStructureService : RecordStructureService,
  ) {}

  ViewDidEnter(): void {
    this.setupCameraElements();
  }

  async setupCameraElements() {
    const container = document.getElementById('scanner-container');
    if (!container) return;
    // Remove previous video/canvas if any
    if (this.videoElement && this.videoElement.parentElement) {
      this.videoElement.parentElement.removeChild(this.videoElement);
    }
    if (this.canvasElement && this.canvasElement.parentElement) {
      this.canvasElement.parentElement.removeChild(this.canvasElement);
    }
    // Container styling
    container.style.position = 'relative';
    container.style.width = '100%';
    container.style.height = '100%';
    container.style.maxWidth = '100vw';
    container.style.maxHeight = '100vh';

    // Create video element (fills container, no inversion)
    this.videoElement = document.createElement('video');
    this.videoElement.setAttribute('playsinline', 'true');
    this.videoElement.style.width = '100%';
    this.videoElement.style.height = '100%';
    this.videoElement.style.objectFit = 'cover';
    this.videoElement.style.position = 'absolute';
    this.videoElement.style.top = '0';
    this.videoElement.style.left = '0';
    this.videoElement.style.zIndex = '1';
    // Remove any inversion
    this.videoElement.style.transform = '';
    container.appendChild(this.videoElement);

    // Canvas sizing logic
    let canvasWidth, canvasHeight;
    if (this.modeName === 'barcode') {
      // Rectangle: wide and short
      canvasWidth = container.offsetWidth * 0.8;
      canvasHeight = container.offsetHeight * 0.25;
    } else {
      // Square: centered
      const minDim = Math.min(container.offsetWidth, container.offsetHeight);
      canvasWidth = minDim * 0.6;
      canvasHeight = minDim * 0.6;
    }

    // Create canvas (centered, visible border)
    let canvasElement = document.getElementById('scan-region') as HTMLCanvasElement
    canvasElement.style.width = '95%'
    canvasElement.style.height = '20%';
    canvasElement.style.position = 'absolute';
    canvasElement.style.top = '40%';
    canvasElement.style.left = '2.5%';
    canvasElement.style.border = '2px solid rgba(0, 255, 0, 0.5)';
    canvasElement.style.borderRadius = '10px';
    canvasElement.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
    canvasElement.style.pointerEvents = 'none'; // Allow clicks to pass
    this.canvasElement = canvasElement;

    container.appendChild(canvasElement);
  }

  ngOnInit() {

  }

  ionViewDidEnter() {
    if (this.typeSelectionItems.length > 0 && !this.selectedType) {
      this.selectedType = this.typeSelectionItems[0];
    }
    let started = false
    this.getUserInfo();
    this.clientConfigurationStore.select(clientConfigurationsSelector.selectClientConfigurationByKeys(['use_qr_code_by_default_in_mobile_scanner'])).pipe(
      filter((keys) => keys), 
      take(1)
    ).subscribe(configurationKeys => {
      if (configurationKeys[0].key == 'use_qr_code_by_default_in_mobile_scanner') {
        if (configurationKeys[0].value) {
          this.changeMode({ detail: { value: 'qrcode' } });
        } else {
          this.startScanner();
        }
        started = true
      }
    })
    if (!started) {
      this.startScanner()
    }
    this.barcodes = [...new Set(this.barcodes)];
  }

  getUserInfo() {
    this.storage.get('user_info').then((userInfo) => {
      if (userInfo) {
        this.userInfo = userInfo;
      }
    });
  }

  playSound(fileName:string) {
    const audio = new Audio('/assets/audio/' + fileName);
    audio.play();
  }

  triggerVibration() {
    Haptics.impact({
      style: ImpactStyle.Heavy,
    });
  }

  async startScanner() {
    if (!this.videoElement || !this.canvasElement) {
      await this.setupCameraElements();
    }
    try {
      const stream = await getCameraStream(this.videoElement!);
      this.videoElement!.srcObject = stream;
      await this.videoElement!.play();
      this.isScanning = true;
      this.scannerLoading = false;
      this.startFrameCapture();
    } catch (err) {
      console.error('Camera error:', err);
      this.scannerLoading = false;
    }
  }

  startFrameCapture() {
    if (this.frameInterval) clearInterval(this.frameInterval);
    this.frameInterval = setInterval(() => this.processFrame(), 300);
  }

  async processFrame() {
    if (!this.videoElement || !this.canvasElement || !this.isScanning) return;
    const base64 = captureFrame(this.videoElement, this.canvasElement, this.modeName);
    if (!base64) return;
    try {
      const scanResult = await BarcodeScanner.scanFromImage({ base64, path: base64 });
      if (scanResult.hasContent && scanResult.content) {
        this.latestResult = scanResult.content;
        if (scanResult.boundingBox) {
          this.scanRegionBox = scanResult.boundingBox;
          drawBoundingBox(this.canvasElement, scanResult.boundingBox);
        }
        if (!this.barcodes.includes(scanResult.content)) {
          this.barcodes.push(scanResult.content);
          if (this.mode === 'single') {
            this.stopScanner();
          } else {
            this.fetchOrderDetails(scanResult.content);
            this.playSound('beep.mp3');
            this.triggerVibration();
            this.triggerFlashEffect();
          }
        }
      } else {
        this.scanRegionBox = undefined;
        this.clearCanvas();
      }
    } catch (err) {
      // Ignore frame errors
    }
  }

  clearCanvas() {
    if (!this.canvasElement) return;
    const ctx = this.canvasElement.getContext('2d');
    if (ctx) ctx.clearRect(0, 0, this.canvasElement.width, this.canvasElement.height);
  }

  openRecordCard(order: any) {
    let buttonClickDetector = new Subject<any>();
    let destroyed = new Subject<any>();
  
    let structuredOrder: RecordCardStructure = this.recordStructureService.formatRecordForCard('sort_and_destribute_card', order) as RecordCardStructure;
  
    if (structuredOrder.footer) {
      structuredOrder.footer = [
        {
          button_theme: "big",
          function_name: "close",
          invisible_domain: false,
          label: "CLOSE",
          local_compute_function: false,
          color: 'danger'
        }
      ];
    }
    structuredOrder.header = undefined;
  
    if (this.activeModal) {
      this.slideOutModal(this.activeModal).then(() => {
        this.createAndShowModal(order, structuredOrder, buttonClickDetector, destroyed);
      });
    } else {
      this.createAndShowModal(order, structuredOrder, buttonClickDetector, destroyed);
    }
  }
  
  createAndShowModal(order: any, structuredOrder: any, buttonClickDetector: Subject<any>, destroyed: Subject<any>) {
    this.modalCtrl.create({
      component: RecordItemComponent,
      cssClass: "windowed-modal-sort-and-destribute fit-window sliding-modal",
      componentProps: {
        recordCardStructure: structuredOrder,
        structuresList: structuredOrder,
        removeMargin: true,
        showInOneLine: true,
        buttonClickDetector: buttonClickDetector,
        tmpSequence: order.sequence
      }
    }).then(modal => {
      this.activeModal = modal;
      modal.present().then(() => {
        const modalElement = document.querySelector('.sliding-modal') as HTMLElement;
        if (modalElement) {
          setTimeout(() => {
            modalElement.classList.add('slide-out');
            setTimeout(() => {
              modal.dismiss();
              this.activeModal = null;
            }, 1000); 
          }, 8000); 
        }
      });
  
      buttonClickDetector.pipe(takeUntil(destroyed)).subscribe(event => {
        this.close();
        if (event.functionName == 'close') return;
        this.doFunction(event.functionName);
      });
  
      modal.onDidDismiss().then(() => {
        destroyed.next({});
        destroyed.unsubscribe();
        buttonClickDetector.unsubscribe();
        this.activeModal = null;
      });
    });
  }
  
  slideOutModal(modal: any) {
    return new Promise<void>(resolve => {
      const modalElement = document.querySelector('.sliding-modal') as HTMLElement;
      if (modalElement) {
        modalElement.classList.add('slide-out');
        setTimeout(() => {
          modal.dismiss();
          resolve();
        }, 2000);
      } else {
        resolve();
      }
    });
  }    

  close() { 
    this.modalCtrl.dismiss()
  }

  doFunction(functionName: OperationTypes) {
    if (functionName in this)
      this[functionName]()
  }

  async fetchOrderDetails(sequence:string) {
    let order = await this.odooRpc.call('rb_delivery.mobile_sort_and_distribute', 'get_order_info', [sequence]);
    if (order.body && order.body.result && order.body.result.result && order.body.result.result[0] && order.body.result.result[0].customer_area && order.body.result.result[0].customer_area[1]) {
      this.orders[sequence] = order.body.result.result[0]
      this.openRecordCard(order.body.result.result[0])
      this.playSound('success_scanning.wav'); 
      this.triggerVibration();
    } else {
      this.removeOrder(sequence)
      this.playSound('fail_sound_1.mp3');
      this.triggerVibration();
    }
  }

  async stopScanner() {
    this.isScanning = false;
    if (this.frameInterval) clearInterval(this.frameInterval);
    if (this.videoElement && this.videoElement.srcObject) {
      const tracks = (this.videoElement.srcObject as MediaStream).getTracks();
      tracks.forEach(track => track.stop());
      this.videoElement.srcObject = null;
    }
    this.clearCanvas();
    
    if (this.barcodes.length === 0) {
      this.modalCtrl.dismiss({ scanningFinished: true });
      return;
    }
    
    if (this.mode === 'multi') {
      // Return all scanned barcodes
      if (this.selectedType) {
        this.modalCtrl.dismiss({ scannedValues: this.barcodes, type: this.selectedType });
      } else {
        this.modalCtrl.dismiss({'barcodes': this.barcodes, 'orders': this.orders});
      }
    } else {
      // Return only the first scanned barcode
      if (this.selectedType) {
        this.modalCtrl.dismiss({ scannedValue: this.barcodes[0], type: this.selectedType });
      } else {
        this.modalCtrl.dismiss(this.barcodes[0]);
      }
    }
  }

  removeOrder(sequence:string) {
    this.barcodes = this.barcodes.filter(order => order !== sequence);
  }

  triggerFlashEffect() {
    this.flash = true;
    setTimeout(() => {
      this.flash = false;
    }, 300);
  }

  async completeWithoutBarcode() {
    if (this.videoElement) {
      try {
        this.stopScanner();
        this.modalCtrl.dismiss('complete');
      } catch (err) {
        console.error('Error completing without barcode:', err);
      }
    }
  }

  async changeMode(event: any) {
    const newMode = event && event.detail && event.detail.value ? event.detail.value : this.modeName;
    if (newMode === this.modeName) return; // No change
    this.modeName = newMode;
    // Update canvas style
    if (this.canvasElement) {
      if (this.modeName === 'barcode') {
        this.canvasElement.style.width = '95%';
        this.canvasElement.style.height = '20%';
        this.canvasElement.style.top = '40%';
        this.canvasElement.style.left = '2.5%';
      } else if (this.modeName === 'qrcode') {
        this.canvasElement.style.width = '80%';
        this.canvasElement.style.height = '80%';
        this.canvasElement.style.top = '10%';
        this.canvasElement.style.left = '10%';
      }
    }
    // Restart scanner if scanning
    if (this.isScanning) {
      await this.stopScanner();
      this.startScanner();
    }
  }

  getImageSrc() {
    return this.modeName === 'barcode' ? '../../../assets/icon/qr_icon.svg' : '../../../assets/icon/barcode_icon.svg';
  }

  getTextSrc() {
    return this.modeName === 'barcode' ? 'qrcode' : 'barcode';
  }
}


enum OperationTypes {
  close = 'close'
}