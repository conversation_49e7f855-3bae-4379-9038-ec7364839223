// Utility functions for camera frame capture and canvas drawing
export async function getCameraStream(videoElement: HTMLVideoElement): Promise<MediaStream> {
  const constraints = {
    video: {
      facingMode: 'environment',
      width: { ideal: 1280 },
      height: { ideal: 720 }
    }
  };
  return await navigator.mediaDevices.getUserMedia(constraints);
}

export function captureFrame(video: HTMLVideoElement, canvas: HTMLCanvasElement, type: 'barcode' | 'qrcode'): string {
  const ctx = canvas.getContext('2d');
  if (!ctx) return '';
  const videoW = video.videoWidth;
  const videoH = video.videoHeight;
  const region = {
    x: videoW * 0.025,
    y: videoH * 0.4,
    width: videoW * 0.95,
    height: videoH * 0.20 
  };
  if (type === 'qrcode') {
    region.x = videoW * 0.1;
    region.y = videoH * 0.1;
    region.width = videoW * 0.8;
    region.height = videoH * 0.8;
  }
  canvas.width = region.width;
  canvas.height = region.height;
  
  ctx.drawImage(
    video,
    region.x, region.y, region.width, region.height,
    0, 0, region.width, region.height               
  );
  return canvas.toDataURL('image/png').split(',')[1];
}

export function drawBoundingBox(canvas: HTMLCanvasElement, box: { x: number; y: number; width: number; height: number }) {
  const ctx = canvas.getContext('2d');
  if (!ctx) return;
  ctx.save();
  ctx.strokeStyle = 'rgba(0,255,0,0.8)';
  ctx.lineWidth = 4;
  ctx.beginPath();
  ctx.rect(box.x, box.y, box.width, box.height);
  ctx.stroke();
  ctx.restore();
}
