// Scanner container styles
.scanner-container {
  position: relative;
  height: 60vh;
  overflow: hidden;
  border-radius: 12px;
  margin-bottom: 16px;
  background-color: #000;
}

.video-container {
  width: 100%;
  height: 100%;
  background-color: #000;
  
  &.barcode {
    // For barcode, optimize for horizontal scan
    position: relative;
    
    &::after {
      content: '';
      position: absolute;
      left: 0;
      width: 100%;
      height: 20%;
      top: 40%;
      border-top: 2px solid rgba(255, 255, 255, 0.5);
      border-bottom: 2px solid rgba(255, 255, 255, 0.5);
      z-index: 10;
      pointer-events: none;
    }
  }
  
  &.qrcode {
    // For QR code, optimize for center square
    position: relative;
  }
}

// Scan overlay
.scan-overlay {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 15;
  width: 100%;
  height: 100%;
  pointer-events: none;
  
  &.barcode {
      position: absolute;
      left: 10%;
      width: 80%;
      height: 20%;
      top: 40%;
      border: 2px solid rgba(0, 150, 255, 0.7);
      box-shadow: 0 0 0 5000px rgba(0, 0, 0, 0.5);
  }
  
  &.qrcode {
      position: absolute;
      left: 15%;
      width: 70%;
      height: 70%;
      top: 15%;
      border: 2px solid rgba(0, 150, 255, 0.7);
      box-shadow: 0 0 0 5000px rgba(0, 0, 0, 0.5);
  }
}

// Result display
.result-container {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background: rgba(0, 0, 0, 0.7);
  padding: 12px;
  color: white;
  max-height: 30%;
  overflow: hidden;
  z-index: 20;
  
  .result-box {
    max-width: 100%;
    word-break: break-all;
    
    h3 {
      margin: 0;
      font-size: 16px;
      opacity: 0.8;
    }
    
    .result-value {
      margin: 4px 0 0;
      font-size: 18px;
      font-weight: bold;
    }
  }
}

// Controls section
.controls {
  padding: 0 0 16px;
  
  ion-button {
    margin-top: 16px;
  }
  
  .camera-controls {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-top: 16px;
  }
  
  .zoom-control {
    margin-top: 16px;
    
    ion-label {
      margin-bottom: 8px;
      display: block;
      text-align: center;
    }
  }
}

// Scan history section
.scan-history {
  margin-top: 16px;
  max-height: 30vh;
  overflow-y: auto;
  
  h3 {
    margin: 0 0 8px;
    padding-left: 16px;
  }
  
  ion-list {
    background: transparent;
  }
}
