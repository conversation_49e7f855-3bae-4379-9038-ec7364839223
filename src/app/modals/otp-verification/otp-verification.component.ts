import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { IonInput, ModalController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { OdooJsonRPC } from 'src/app/services/odooJsonRPC';
import { dialogService } from 'src/app/services/error-handlar-service';

@Component({
  selector: 'app-otp-verification',
  templateUrl: './otp-verification.component.html',
  styleUrls: ['./otp-verification.component.scss'],
})
export class OtpVerificationComponent implements OnInit {
  @Input() recordId?: string;
  @ViewChild('digit1', { static: false }) digit1!: IonInput;
  
  otpForm!: FormGroup;
  isLoading: boolean = false;
  otpResent: boolean = false;
  resendDisabled: boolean = true;
  countdown: number = 30;
  timer: any;
  message:string = 'OTP_RESENT_SUCCESSFULLY'
  failed: boolean = false;
  
  constructor(
    private modalCtrl: ModalController,
    private formBuilder: FormBuilder,
    private odooRpc: OdooJsonRPC,
    private translate: TranslateService,
    private dialogService: dialogService
  ) { }

  ngOnInit() {
    this.initForm();
    this.startCountdown();
    this.resendOtp(true)
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.digit1?.setFocus();
    }, 300);
  }

  initForm() {
    this.otpForm = this.formBuilder.group({
      digit1: ['', [Validators.required, Validators.pattern(/^[0-9]$/)]],
      digit2: ['', [Validators.required, Validators.pattern(/^[0-9]$/)]],
      digit3: ['', [Validators.required, Validators.pattern(/^[0-9]$/)]],
      digit4: ['', [Validators.required, Validators.pattern(/^[0-9]$/)]],
      digit5: ['', [Validators.required, Validators.pattern(/^[0-9]$/)]],
      digit6: ['', [Validators.required, Validators.pattern(/^[0-9]$/)]],
    });
  }

  startCountdown() {
    this.resendDisabled = true;
    this.countdown = 30;
    
    clearInterval(this.timer);
    this.timer = setInterval(() => {
      this.countdown--;
      if (this.countdown <= 0) {
        clearInterval(this.timer);
        if (!this.failed){ 
          this.resendDisabled = false;
        }
      }
    }, 1000);
  }



  onDigitInput(event: any, nextInput?: any, prevInput?: any) {
    if (event.target.value.length === 1 && nextInput  && !(event.key === 'Backspace' || event.inputType === 'deleteContentBackward')) {
      nextInput.setFocus();
    } 
    else if (prevInput && (event.key === 'Backspace' || event.inputType === 'deleteContentBackward')) {
      if (event.target.value.length === 0) {
        prevInput.setFocus();
      }
    }
  }

  getOtp(): string {
    const { digit1, digit2, digit3, digit4, digit5, digit6 } = this.otpForm.value;
    return `${digit1}${digit2}${digit3}${digit4}${digit5}${digit6}`;
  }

  async verifyOtp() {
    if (this.otpForm.invalid) {
      this.otpForm.markAllAsTouched();
      return;
    }

    this.isLoading = true;
    const otpCode = this.getOtp();

    try {
      const response = await this.odooRpc.call('rb_delivery.order', 'check_otp_code', [this.recordId, otpCode]);
      
      if (response?.body?.result?.success) {
        this.modalCtrl.dismiss({ verified: true, otp: otpCode });
      } else {
        const errorMessage = response?.body?.result?.message || 'INVALID_OTP';
        this.showError(errorMessage);
      }
    } catch (error) {
      this.showError('ERROR_VALIDATING_OTP');
    } finally {
      this.isLoading = false;
    }
  }

  async resendOtp(force?: boolean) {
    if (this.resendDisabled && !force) return;
    
    this.isLoading = true;
    try {
      const response = await this.odooRpc.call('rb_delivery.order', 'send_otp_code', [this.recordId]);
      
      if (response?.body?.result?.success) {
        this.message = this.translate.instant(response?.body?.result?.result?.message) || this.translate.instant('OTP_RESENT_SUCCESSFULLY');
        this.failed = response?.body?.result?.result?.status !== 'success';
        if (this.failed && response?.body?.result?.result?.bypass_otp) {
          this.message = this.translate.instant('OTP_BYPASSED_BECAUSE_OF_ERROR');
          let otpCode = response?.body?.result?.result?.otp_code || '000000';
          this.otpForm.patchValue({
            digit1: otpCode[0],
            digit2: otpCode[1],
            digit3: otpCode[2],
            digit4: otpCode[3],
            digit5: otpCode[4],
            digit6: otpCode[5]
          });
          this.countdown = -1;
          this.resendDisabled = true;
        } else {
          this.otpForm.reset();
          this.startCountdown();
          setTimeout(() => {
            this.otpResent = false;
          }, 3000);
        }
        this.otpResent = true;
        
      } else {
        const errorMessage = response?.body?.result?.message || 'ERROR_SENDING_OTP';
        this.showError(errorMessage);
      }
    } catch (error) {
      this.showError('ERROR_SENDING_OTP');
    } finally {
      this.isLoading = false;
    }
  }

  showError(message: string) {
    this.dialogService.error({
      message: this.translate.instant('OTP_VERIFICATION_FAILED'),
      input: this.translate.instant('OTP_VERIFICATION_FAILED'),
      code: '1300'
    });
  }

  dismiss() {
    clearInterval(this.timer);
    this.modalCtrl.dismiss();
  }
}
