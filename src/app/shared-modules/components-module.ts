import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';
import { FormCreatorComponent } from '../components/form-components/form-creator/form-creator.component';
import { FormInputComponent } from '../components/form-components/form-input/form-input.component';
import { dynamicDashboardComponent } from '../components/dynamic-dashboard/dynamic-dashboard.component';
import { TemplateOneDashboardComponent } from '../components/template-one-dashboard/template-one-dashboard.component';
import { TemplateTwoDashboardComponent } from '../components/template-two-dashboard/template-two-dashboard.component';
import { TemplateFiveDashboardComponent } from '../components/template-five-dashboard/template-five-dashboard.component';
import { TemplateFourDashboardComponent } from '../components/template-four-dashboard/template-four-dashboard.component';
import { TemplateSixDashboardComponent } from '../components/template-six-dashboard/template-six-dashboard.component';

import {MatProgressSpinnerModule} from '@angular/material/progress-spinner';
import { FormFooterComponent } from '../components/form-components/form-footer/form-footer.component';
import { RecordCreatorPage } from '../modals/record-creator/record-creator.page';
import { RecordItemComponent } from '../components/record-item/record-item.component';
import { RecordItemContentComponent } from '../components/record-item-content/record-item-content.component';
import { RecordItemFooterComponent } from '../components/record-item-footer/record-item-footer.component';
import { RecordItemHeaderComponent } from '../components/record-item-header/record-item-header.component';
import { TranslateModule } from '@ngx-translate/core';
import { MatStepperModule } from '@angular/material/stepper';
import { dynamicDashboardItemComponent } from '../components/dynamic-dashboard-item/dynamic-dashboard-item.component';
import { dynamicDashboardChartComponent } from '../components/dynamic-dashboard-chart/dynamic-dashboard-chart.component';
import { dynamicDashboardNoOfOrdersComponent } from '../components/dynamic-dashboard-no-of-orders/dynamic-dashboard-no-of-orders.component';
import { DynamicSelectionComponent } from '../components/dynamic-selection/dynamic-selection.component';
import { BusinessStoresPage } from '../modals/business-stores-page/business-stores.page';
import { ChangePasswordComponent } from '../components/change-password/change-password.component';
import { SuccessConfirmationMessageComponent } from '../components/success-confirmation-message/success-confirmation-message.component';
import { StatusActionComponent } from '../components/status-action/status-action.component';
import { DateSelectorComponent } from '../modals/date-selector/date-selector.component';
import { ContactUsComponent } from '../components/contact-us/contact-us.component';
import { UpdateMultiOrdersComponent } from '../components/update-multi-orders/update-multi-orders.component';
import { FollowerOrderScannerComponent } from '../components/follower-order-scan/follower-order-scanner.component';
import { FollowOrdersScanner } from '../modals/follow-orders-scanner/folllow-orders-scanner.component';
import { FollowOrderCreatorComponent } from '../components/follow-order-creator/follow-order-creator.component';
import { BarcodeScanComponent } from '../modals/barcode-scan/barcode-scan.component';
import { dynamicDashboardHorizontalChartComponent } from '../components/dynamic-dashboard-horizontal-chart/dynamic-dashboard-horizontal-chart.component';
import { OrderChatComponent } from '../components/order-chat/order-chat.component';
import { ChatItemComponent } from '../components/chat-item/chat-item.component';
import { ConfirmRequestMoneyCollectionComponent } from '../components/confirm-request-money-collection/confirm-request-money-collection.component';
import { SuccessRequestMoneyCollectionComponent } from '../components/success-request-money-collection/success-request-money-collection.component';
import { TimerAlertComponent } from '../components/timer-alert/timer-alert.component';
import { NgCircleProgressModule } from 'ng-circle-progress';
import { OpenScannedElementsComponent } from '../components/open-scanned-elements/open-scanned-elements.component';
import { OrderHistoryComponent } from '../modals/order-history/order-history.component';
import { ReportsPage } from '../modals/reports-page/reports-page';
import { ErrorDialogComponent } from '../components/error-dialog/error-dialog.component';
import { TemplateFiveNoOfOrdersComponent } from '../components/template-five-no-of-orders/template-five-no-of-orders.component';
import { TemplateFiveHorizontalChartComponent } from '../components/template-five-horizontal-chart/template-five-horizontal-chart.component';
import { PdfDialogComponent } from '../components/pdf-dialog/pdf-dialog.component';
import { CustomizedSplashScreenComponent } from '../components/customized-splash-screen/customized-splash-screen.component';
import { LocationSelectorComponent } from '../modals/location-selector/location-selector.component';
import { GoogleMapsModule } from '@angular/google-maps';
import { CreateCollectionConfirmationComponent } from '../components/create-collection-confirmation/create-collection-confirmation.component';
import { SuccessCreateCollectionComponent } from '../components/success-create-collection/success-create-collection.component';
import { SortAndDestributeMenuComponent } from '../components/sort-and-destribute/sort-and-destribute-menu/sort-and-destribute-menu.component';
import { AttachmentModal } from '../modals/attachments-modal/attachment-modal';
import { AttachmentPreviewComponent } from '../modals/attachment-preview/attachment-preview.component';
import { RouteCollectionComponent } from '../components/route-collection/route-collection.component';
import { CreateRouteCollectionComponent } from '../components/create-route-collection/create-route-collection.component';
import { AnnouncementCardComponent } from '../components/announcement-card/announcement-card.component';
import { SolveStuckProgressBarComponent } from '../components/solve-stuck-progress-bar/solve-stuck-progress-bar.component';
import { DriversRoundComponent } from '../components/drivers-round/drivers-round.component';
import { DriversRoundSummaryComponent } from '../components/drivers-round/drivers-round-summary/drivers-round-summary.component';
import { OrderDetailComponent } from '../components/order-detail/order-detail.component';
import { OtpVerificationComponent } from '../modals/otp-verification/otp-verification.component';
import { ConnectionLostComponent } from '../components/connection-lost/connection-lost.component';





@NgModule({
    exports: [
        FormCreatorComponent,
        FormInputComponent,
        FormFooterComponent,
        dynamicDashboardComponent,
        TemplateOneDashboardComponent,
        TemplateTwoDashboardComponent,
        TemplateFiveDashboardComponent,
        TemplateFourDashboardComponent,
        dynamicDashboardItemComponent,
        dynamicDashboardChartComponent,
        dynamicDashboardNoOfOrdersComponent,
        RecordCreatorPage,
        RecordItemComponent,
        RecordItemContentComponent,
        RecordItemFooterComponent,
        RecordItemHeaderComponent,
        DynamicSelectionComponent,
        RecordCreatorPage,
        DynamicSelectionComponent,
        ChangePasswordComponent,
        SuccessConfirmationMessageComponent,
        StatusActionComponent,
        ContactUsComponent,
        UpdateMultiOrdersComponent,
        StatusActionComponent,
        dynamicDashboardHorizontalChartComponent,
        OrderChatComponent,
        ChatItemComponent,
        ConfirmRequestMoneyCollectionComponent,
        SuccessRequestMoneyCollectionComponent,
        TimerAlertComponent,
        OpenScannedElementsComponent,
        FollowerOrderScannerComponent,
        FollowOrdersScanner,
        FollowOrderCreatorComponent,
        ReportsPage,
        ErrorDialogComponent,
        TemplateFiveNoOfOrdersComponent,
        TemplateFiveHorizontalChartComponent,
        PdfDialogComponent,
        CustomizedSplashScreenComponent,
        CreateCollectionConfirmationComponent,
        SuccessCreateCollectionComponent,
        SortAndDestributeMenuComponent,
        RouteCollectionComponent,
        CreateRouteCollectionComponent,
        OrderDetailComponent,
        AnnouncementCardComponent,
        SolveStuckProgressBarComponent,
        DriversRoundComponent,
        DriversRoundSummaryComponent,
        OtpVerificationComponent,
        TemplateSixDashboardComponent,
        ConnectionLostComponent


    ],
    imports: [
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        IonicModule,
        MatProgressSpinnerModule,
        TranslateModule,
        GoogleMapsModule,
        MatStepperModule,
        NgCircleProgressModule.forRoot({
        "backgroundGradient": true,
        "backgroundColor": "#333333",
        "backgroundGradientStopColor": "#181616",
        "backgroundStrokeWidth": 50,
        "radius": 60,
        "space": 7,
        "toFixed": 0,
        "maxPercent": 250,
        "units": " Point",
        "unitsColor": "#483500",
        "outerStrokeGradient": true,
        "outerStrokeWidth": 6,
        "outerStrokeColor": "#cf4646",
        "outerStrokeGradientStopColor": "#3c7380",
        "innerStrokeColor": "#333333",
        "innerStrokeWidth": 9,
        "titleColor": "#ffffff",
        "subtitleColor": "#483500",
        "showSubtitle": false,
        "showUnits": false,
        "startFromZero": false
          })

    ],
    declarations: [
        FormCreatorComponent,
        FormInputComponent,
        FormFooterComponent,
        dynamicDashboardComponent,
        TemplateOneDashboardComponent,
        TemplateTwoDashboardComponent,
        TemplateFiveDashboardComponent,
        TemplateFourDashboardComponent,
        dynamicDashboardItemComponent,
        dynamicDashboardChartComponent,
        dynamicDashboardNoOfOrdersComponent,
        RecordCreatorPage,
        RecordItemComponent,
        RecordItemContentComponent,
        RecordItemFooterComponent,
        RecordItemHeaderComponent,
        DynamicSelectionComponent,
        BusinessStoresPage,
        RecordCreatorPage,
        DynamicSelectionComponent,
        ChangePasswordComponent,
        SuccessConfirmationMessageComponent,
        StatusActionComponent,
        DateSelectorComponent,
        OrderHistoryComponent,
        LocationSelectorComponent,
        ContactUsComponent,
        UpdateMultiOrdersComponent,
        BarcodeScanComponent,
        AttachmentModal,
        AttachmentPreviewComponent,
        dynamicDashboardHorizontalChartComponent,
        OrderChatComponent,
        ChatItemComponent,
        ConfirmRequestMoneyCollectionComponent,
        SuccessRequestMoneyCollectionComponent,
        TimerAlertComponent,
        OpenScannedElementsComponent,
        FollowerOrderScannerComponent,
        FollowOrdersScanner,
        FollowOrderCreatorComponent,
        ReportsPage,
        ErrorDialogComponent,
        TemplateFiveNoOfOrdersComponent,
        TemplateFiveHorizontalChartComponent,
        PdfDialogComponent,
        CustomizedSplashScreenComponent,
        CreateCollectionConfirmationComponent,
        SuccessCreateCollectionComponent,
        SortAndDestributeMenuComponent,
        RouteCollectionComponent,
        CreateRouteCollectionComponent,
        OrderDetailComponent,
        AnnouncementCardComponent,
        SolveStuckProgressBarComponent,
        DriversRoundComponent,
        DriversRoundSummaryComponent,
        OtpVerificationComponent,
        TemplateSixDashboardComponent,
        ConnectionLostComponent

    ]

})
export class SharedComponentModule { }
