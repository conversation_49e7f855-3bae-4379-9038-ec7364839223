<div style="padding-top: var(--ion-safe-area-top);">
  <ion-toolbar mode="md" *ngIf="showToolbar || customIntro" style="--border-width:0px;padding-inline: 12px; --background: transparent;padding-top: 15px;z-index: 2;">
    <ng-container *ngIf="!trackedOrder && !TrackedErrorMessage && formName!='login_form' && formName!='signup_form'">
        <ion-searchbar *ngIf="showToolbar" [formControl]="searchControl" placeholder="{{'TRACKING_ORDER' | translate }}"
        style="padding: 0 10px;font-family: 'cairo'; --border-radius: 20px; --padding-bottom: 0; --padding-top: 0; --background: #fff;opacity: 0.3; border: 1px solid black; border-radius: 50px; background: #fff; zoom: 80%;" mode='ios'>
        </ion-searchbar>
        <ion-buttons slot="end">
            <ion-button style="zoom: 1.5;" (click)="clearTrackingSearch()" *ngIf="trackedOrder || TrackedErrorMessage && showToolbar">
            <ion-icon name="close-circle-outline" style="color:red"></ion-icon>
            </ion-button>
            <ion-button style="zoom: 1.5;" (click)="openLanguageSelection()">
            <ion-icon src="../../../assets/icon/language_login.svg" style="color: var(--ion-color-primary);box-shadow: none !important;"></ion-icon>
            </ion-button>
        </ion-buttons>
    </ng-container>
    <ion-title *ngIf="formName=='login_form' || formName=='signup_form'">
        {{(formName=='signup_form'?'SIGN_UP':'LOGIN') | translate}}
    </ion-title>
    <ion-buttons *ngIf="showToolbar && isModalOpen" slot="start">
        <ion-button color="dark" (click)="back()">
            <ion-icon name="chevron-back-outline" ></ion-icon>
        </ion-button>
    </ion-buttons>
    <ng-container *ngIf="trackedOrder || TrackedErrorMessage || formName=='login_form' || formName=='signup_form'">
        <ion-title *ngIf="trackedOrder || TrackedErrorMessage">
            {{"ORDER_DETAILS" | translate}}
        </ion-title>
        <ion-buttons slot="end">
            <ion-button style="zoom: 1.5;" (click)="openLanguageSelection()">
                <ion-icon src="../../../assets/icon/language_login.svg" style="color: var(--ion-color-primary);box-shadow: none !important;"></ion-icon>
            </ion-button>
        </ion-buttons>
        <ion-buttons *ngIf="trackedOrder || TrackedErrorMessage" slot="start">
            <ion-button color="dark" (click)="clearTrackingSearch()">
                <ion-icon [name]="isModalOpen?'close-circle-outline':'chevron-back-outline'" ></ion-icon>
            </ion-button>
        </ion-buttons>
    </ng-container>
    
    
  </ion-toolbar>
</div>

<ion-alert *ngIf="languageAlertInputs.length >0" #alertLanguage header="{{ 'SELECT_YOUR_LANGUAGE' | translate}}" [buttons]="languageAlertButtons"  [inputs]="languageAlertInputs"></ion-alert>
<div *ngIf="!trackedOrder && !TrackedErrorMessage" [ngClass]="{'custom-intro':customIntro, 'hidden': loadingForm}">
    <div *ngIf="showToolbar" style=" padding-bottom: 10px; border-bottom: 1px solid lightgray; width: 90%; margin: 0 auto;"></div>
    
    <ion-col *ngIf="customIntro" size="4" style="text-align: center;">

        <div style="width: 60%; margin:0 auto;max-width: 380px;">
            <img src="../../../assets/logo.png" alt="Logo"/>

        </div>
    </ion-col>
    <ion-grid style="max-width: 450px;" *ngIf="!steps && !disableEdit" class="ion-padding"   [ngStyle]="{'max-width': '430px','background-image': 'url(' + formImgSource + ')','background-size': 'contain','background-repeat': 'no-repeat','background-position-x': 'center'}">
        <ion-row [ngStyle]="{'margin-bottom': isIsolated?'0':'45px'}" style="display: flex; flex-direction: column ; gap:16px ;">
            <ng-container *ngFor="let input of formInputs;let i = index;"  >
                <app-form-input *ngIf="(input.position=='in_form' || input.is_separator) && !input.invisible"
                [isModalOpen]="isModalOpen"
                [field]="input.field" 
                [parentField]="input.parent_field" 
                [domain]="input.domain"
                [searchDomain]="input.search_domain"
                [limitPerSearch]="input.limit_per_search"
                [value]="input.value"
                [haveImage]="input.have_image"
                [imageSource]="input.image_source"
                [imageFieldName]="input.image_field_name"
                [parentValue]="input.parent_value"
                [isSeparator]="input.is_separator"
                [firstSeparator]="input.is_separator && i==0"
                [color]="input.color"
                [background]="input.background"
                [separatorTitle]="input.separator_title"
                [showBarcodeScanner] = "input.show_barcode_scanner"
                [showLocationSelector] = "input.show_location_selector"
                [showVoiceToTextAbility] = "input.show_voice_to_text_ability"
                [loading] = "input.loading"
                [success] = "input.success"
                [fail] = "input.fail"
                [isFormCreator] = "input.is_form_creator"
                [formName] = "formName"
                [isButton] = "input.is_button"
                [buttonFunction] = "input.button_function"
                [buttonText] = "input.button_text"
                [buttonIcon] = "input.button_icon"
                [selectionItems] = "input.selection_items"
                (selectedValue)="updateValue(input,$event)"
                (onLocationSet)="updateLocation($event)"
                (selectedParentValue)="updateParentValue(input,$event)"
                (setParentLoading)="setParentLoading($event,input)"
                (buttonPress)="doFunction(input)"
                [validationMessage]="input.validationMessage"
                [isMonetary]="input.is_monetary"
                (updateMappingFields)="updateMappingFieldsInfo($event,input)"
                [isAutoFillMappingFieldsEnabled]="input.is_auto_fill_mapping_fields_enabled"
                [mappingFields]="input.mapping_relation_fields"
                [validations]="input.validations"
                [connected_field_to_selection_modal]="input.connected_field_to_selection_modal"
                [search_domain_inside_selection_modal] ="input.search_domain_inside_selection_modal"
                [showClientHistory] = "input.show_client_history"
                [formInputs] = "input.isolated_form"
                [destroyed]="destroyed"
                [numberOfInputs] = "getNumberOfInputs(formInputs)"
                [statusId]="statusId"
                >
                </app-form-input>
            </ng-container>
        </ion-row>
        
    </ion-grid>
    
    <mat-horizontal-stepper #stepper labelPosition="bottom" *ngIf="steps && steps.length > 0 && !disableEdit">
        <mat-step *ngFor="let step of steps; let i = index;">
            <ion-grid style="max-width: 450px;" class="ion-no-padding">
                <ion-row style="display: flex; flex-direction: column ; gap:16px ;">
                    <ng-container *ngFor="let input of step.form_inputs;let i = index;" >
                        <app-form-input *ngIf="(input.position=='in_form' || input.is_separator) && !input.invisible"
                        [isModalOpen]="isModalOpen"
                        [field]="input.field" 
                        [parentField]="input.parent_field" 
                        [domain]="input.domain"
                        [searchDomain]="input.search_domain"
                        [limitPerSearch]="input.limit_per_search"
                        [value]="input.value"
                        [haveImage]="input.have_image"
                        [imageSource]="input.image_source"
                        [imageFieldName]="input.image_field_name"
                        [parentValue]="input.parent_value"
                        [isSeparator]="input.is_separator"
                        [firstSeparator]="input.is_separator && i==0"
                        [color]="input.color"
                        [background]="input.background"
                        [separatorTitle]="input.separator_title"
                        [showBarcodeScanner] = "input.show_barcode_scanner"
                        [showLocationSelector] = "input.show_location_selector"
                        [loading] = "input.loading"
                        [success] = "input.success"
                        [fail] = "input.fail"
                        [isFormCreator] = "input.is_form_creator"
                        [formName] = "formName"
                        [isButton] = "input.is_button"
                        [buttonFunction] = "input.button_function"
                        [buttonText] = "input.button_text"
                        [buttonIcon] = "input.button_icon"
                        [selectionItems] = "input.selection_items"
                        (selectedValue)="updateValue(input,$event)"
                        (selectedParentValue)="updateParentValue(input,$event)"
                        (setParentLoading)="setParentLoading($event,input)"
                        (buttonPress)="doFunction(input)"
                        [validationMessage]="input.validationMessage"
                        [isMonetary]="input.is_monetary"
                        [isAutoFillMappingFieldsEnabled]="input.is_auto_fill_mapping_fields_enabled"
                        [mappingFields]="input.mapping_relation_fields"
                        (updateMappingFields)="updateMappingFieldsInfo($event,input)"
                        [connected_field_to_selection_modal]="input.connected_field_to_selection_modal"
                        [search_domain_inside_selection_modal] ="input.search_domain_inside_selection_modal"
                        [showClientHistory] = "input.show_client_history"
                        [formInputs] = "input.isolated_form"
                        [destroyed]="destroyed"
                        [numberOfInputs] = "getNumberOfInputs(formInputs)"
                        >
                        </app-form-input>
                    </ng-container>
                    
                    
                </ion-row>
            </ion-grid>
        </mat-step>



    </mat-horizontal-stepper> 
    <app-form-footer *ngIf="!disableEdit" [transparent]="showToolbar || formName=='login_form' || formName=='signup_form'" [formFooterInputs]="formFooterInputs" (buttonPress)="doFunction($event)" (nextStep)="nextStep()" (prevStep)="prevStep()" [showNext]="showNext" [showBack]="showBack" [navigateButtons]="steps && steps.length>0"></app-form-footer>

    <ion-grid style="max-width: 450px;" *ngIf="disableEdit" class="ion-padding">
        <ion-row style="display: flex; flex-direction: column ; gap:16px ;">
            <ng-container *ngFor="let input of formInputs;let i = index;"  >
                <app-form-input *ngIf="input.is_separator"
                [isModalOpen]="isModalOpen"
                [isSeparator]="input.is_separator"
                [firstSeparator]="input.is_separator && i==0"
                [separatorTitle]="input.separator_title">
                </app-form-input>
                <ion-label *ngIf="input.position=='in_form'&&!input.is_button && input.field && !input.invisible">
                    {{checkDisplayValue(input.field,input.value)}}
                </ion-label>
            </ng-container>
        </ion-row>
        
    </ion-grid>
</div>

<div *ngIf="loadingForm">
  <ion-spinner name="crescent"  class="spinner">
          
  </ion-spinner>
  <span class="spinner-text">{{'LOADING'|translate}}</span>
</div>

<div *ngIf="trackedOrder || TrackedErrorMessage">
    <ion-grid class="ion-padding">
      <ion-row>  
        <ion-col size="12">
          <ion-card mode="ios" *ngIf="trackedOrder && trackedOrder.length > 0" style="box-shadow: var(--ion-color-primary) 0px 5px 20px -8px; border-radius: 12px;" class="ion-margin">
            <ion-card-content style="padding: 10px;">
              <ion-item lines="full" *ngIf="trackedOrder[0].sequence" class="ion-align-items-center" style="--background: var(--ion-color-primary);color: white;">
                
                <ion-label slot="start" class="ion-text-wrap" style="color: white;">
                  <h2 style="font-weight: bold;" class="ion-no-margin">{{ "SEQUENCE" | translate }}</h2>
                </ion-label>
                <ion-label slot="end" class="ion-text-wrap" style="color: white; display: flex; flex-direction: row-reverse; position: absolute; inset-inline-end: 0;">
                    <ion-icon style="font-size: 10px; align-self: center; margin-inline-start: 3px;" slot="end" src="assets/icons/hash.svg" slot="start" class="ion-margin-start"></ion-icon>
                    <h2 class="ion-no-margin" style="color: white;">{{ trackedOrder[0].sequence }}</h2>
                </ion-label>
              </ion-item>
  
              <ion-item lines="none" *ngIf="trackedOrder[0].reference_id" class="ion-align-items-center" style="--background: var(--ion-color-primary);color: white;">
                
                <ion-label slot="start" class="ion-text-wrap" style="color: white;">
                  <h2 style="font-weight: bold;" class="ion-no-margin">{{ "REFERENCE_ID" | translate }}</h2>
                </ion-label>
                <ion-label slot="end" class="ion-text-wrap" style="color: white; display: flex; flex-direction: row-reverse; position: absolute; inset-inline-end: 0;">
                    <ion-icon size="large" slot="end" src="assets/icons/airplane.svg" slot="start" class="ion-margin-start"></ion-icon>
                    <h2 class="ion-no-margin" style="color: white; align-self: center;">{{ trackedOrder[0].reference_id }}</h2>
                </ion-label>
              </ion-item>
  
            </ion-card-content>
          </ion-card>
          <div style="padding-bottom: 15px; border-bottom: 1px solid lightgray; width: 90%; margin: 0 auto; margin-bottom: 28px;"></div>
          <div *ngIf="!TrackedErrorMessage" style="zoom: 80%; padding-top: 30px;" [ngClass]="{'timeline-ar': translate.currentLang === 'ar', 'timeline': translate.currentLang !== 'ar'}" >
            <div [ngClass]="{'timeline-item-ar': translate.currentLang === 'ar', 'timeline-item': translate.currentLang !== 'ar'}" *ngFor="let step of trackedOrder;let i = index;">
              <div style="background: var(--forms-background);" [ngClass]="{'timeline-icon-ar': translate.currentLang === 'ar', 'timeline-icon': translate.currentLang !== 'ar'}">
                <ion-icon [src]="i == 0?'assets/icons/checkTimeLine.svg':'assets/icons/nonCheckTimeLine.svg'"></ion-icon>
              </div>
              <div class="timeline-content" [ngClass]="{'timeline-content-ar': translate.currentLang === 'ar', 'timeline-content': translate.currentLang !== 'ar'}">
                <ion-icon [src]="'assets/icons/info.svg'" style="width: 45px;height: 45px;margin-inline-end: 15px;"></ion-icon>
                <div class="timeline-text ion-no-margin">
                  <h3 class="timeline-title ion-no-margin">{{ step.status | translate}}</h3>
                  <span class="time">{{ step.created_on }}</span>
                </div>
              </div>
              <div [ngClass]="{'line-ar': translate.currentLang === 'ar', 'line': translate.currentLang !== 'ar'}">
              </div>
            </div>
          </div>
  
          <div *ngIf="!trackedOrder || trackedOrder.length == 0" class="ion-padding">
            
            <ion-text color="medium">
              <h4>{{ TrackedErrorMessage }}</h4>
            </ion-text>
          </div>
        </ion-col>
      </ion-row>
    </ion-grid>
</div>
