.margin-items {
  margin-block: 5px;
}

.input-fill-outline.input-shape-round {
  --border-color: blue;
  --border-radius: 40px;

}

.datetime-input {
  display: flex;
  justify-content: space-between;
  border: 1px solid var(--ion-color-medium);
  align-items: center;
  border-radius: 8px;
  margin-bottom: 5px;
  height: 50px;
  padding: 15px;
  background-color: var(--ion-color-white);

  ion-icon {
    font-size: 25px;
  }

}

ion-datetime-button::part(native) {
  background: var(--ion-color-white);
}

.input-style {
  --border-radius: 8px !important;
  min-height: 48px !important;
  --background: white !important;
  --highlight-color: var(--ion-color-primary);
}

.input-icon-wrap {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.input-icon{
  height: 48px;
  width: 38px;
  font-size: 30px;
}

ion-col {
  height: fit-content !important;
  display: block !important;
}

ion-item-divider {
  --background: transparent !important;
  padding: 0;
  padding-inline-start: 9px;
  padding-top: 16px;
  border-top: 2px solid;
}

.date-label-container {
  width: 0;
  overflow: visible;

  ion-label {
    text-align: center;
    font-size: 12px;
    position: relative;
    top: -28px;
    background: white;
    min-width: max-content;
    display: block;
  }
}
.many2one-label-container{
  width: 0;
  overflow: visible;
  position: absolute;
  z-index: 999;
  width: 100%;
  ion-label {
    text-align: center;
    font-size: 12px;
    width: fit-content;
    position: relative;
    top: -10px;
    inset-inline-start: 12px;
    background: white;
    min-width: max-content;
    display: block;
  }
}
.date-value-label{
  display: flex;
  justify-content: flex-start;
  position: absolute;
}
ion-label{
  padding-inline: 3px;
}
ion-modal {
  background: #00000020;
}

.olivery-item-for-input {
  --background: var(--ion-color-white);
  background: var(--ion-color-white);;
  border-radius: 8px;
  border: 1px solid var(--ion-color-medium);
  height: 50px !important;
  width: -webkit-fill-available;
  &::part(native){
    height: 48px;
  }
}

.boolean-style{
  border: 1px solid var(--ion-color-medium);
  border-radius: 8px;
  --background: var(--ion-color-white);
  margin-bottom: 5px;
}

.many2many-style{
  overflow: scroll;
  white-space: nowrap;
  max-width: 90vw;
}

.scroll-indicator{
  position: absolute;
  inset-inline-end: 0;
  z-index: 999;
  height: 100%;
  border-radius: 0;
  display: flex;
  align-items: center;
  width: 38px;
}

.tag{
  background: var(--ion-color-primary-contrast);
  color: var(--ion-color-primary);
}

.tagNew{
  background: var(--ion-color-success);
  color: var(--ion-color-white);
  overflow: visible;
}
.newLabel{
  position: absolute;
  top: -4px;
  background: var(--ion-color-danger);
  inset-inline-start: 0;
  font-size: 9px;
  border-radius: 10px;
}

ion-toggle {
  padding: 12px;

  --track-background: var(--ion-color-tertiary);
  --track-background-checked: var(--ion-color-tertiary);

  --handle-background: var(--ion-color-medium);
  --handle-background-checked: var(--ion-color-primary);

  --handle-width: 27px;
  --handle-height: 27px;
  --handle-max-height: auto;
  --handle-spacing: 3px;
  --handle-box-shadow: none;

  

}


.validation {
  display: flex;
  align-items: center;
  color: #D63301;
  font-size: 12px;
  ion-icon {
    font-size: 25px;
  }
}

.input-error-style{
  --border-radius: 8px !important;
  min-height: 48px !important;
  --background: white !important;
  --border-color:red !important;
  color: red !important;
  border-color: red !important;
}
canvas {
  display: block;
  margin: 0 auto;
  touch-action: none;
}

.separatorDivider{
  flex-direction: column;
}

ion-thumbnail {
  --size: auto; /* Adjusts the thumbnail size to its content */
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

ion-thumbnail img {
  width: 100%; /* Makes the image fill the column width */
  height: auto; /* Maintains the aspect ratio */
  max-height: 200px; /* Adjust this value to fit your design */
}

.remove-icon {
  position: absolute;
  top: 0;
  right: 0;
  color: red;
  cursor: pointer;
  font-size: 24px; /* Makes the close icon larger */
}


.selection-container{
  --background:linear-gradient(to bottom, var(--ion-color-primary) 20px, var(--ion-color-white) 20px) !important;
  border-radius: 8px;
  border: 1px solid var(--ion-color-medium);
  width: -webkit-fill-available;
}

.centered-div {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  text-align: center; /* This ensures text inside ion-label is centered */
}

.buttons-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  margin-top: 10px;
  
}

.login_button {
  box-shadow: rgba(0, 0, 0, 0.4) 0px 30px 90px;
  font-family: 'cairo';
  font-weight: bold;
}

.buttons-container ion-button {
  margin: 5px; /* Add margin if you want spacing between buttons */
}
