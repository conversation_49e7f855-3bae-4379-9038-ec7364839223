<ion-col [ngClass]="validationMessage && validationMessage.length > 0 && formInputs && !loading ?'input-error-style olivery-item-for-input':formInputs && !loading?'input-style olivery-item-for-input':''" [ngStyle]="{'pointer-events': showBarcodeScanner ? 'auto' : (field.readonly ? 'none' : 'auto'),}" *ngIf="field" [id]="field.id"  class="ion-no-padding" style="height: fit-content !important;width: inherit;" (focusin)="onFocus()" size="12">
  <div *ngIf="field.readonly" [ngStyle]="{'width': showBarcodeScanner ? 'calc(100% - 38px)' : '100%'}" style="position: absolute; z-index: 99; background: #3d393742; height: 90%; border-radius: 10px;"></div>
  <div style="padding-bottom: 5px; width: inherit;display: flex;justify-content: space-evenly;" *ngIf="field && (field.ttype=='many2one' || field.ttype=='many2many' || field.ttype=='one2many')"  >
    <div *ngIf="(value && field.ttype=='many2one' && !loading) || (value && value.length>0 && !loading)" class="many2one-label-container">
      <ion-label>{{field.placeholder | translate}}</ion-label>
    </div>
    <ion-item [disabled]="loading" class="olivery-item-for-input " [ngStyle]="{'border':formInputs?'none':''}" [ngClass]="validationMessage && validationMessage.length > 0 ?'input-error-style':'input-style'" (click)="openSelector()" button mode="ios" lines="none">
      <ion-ripple-effect *ngIf="!loading" [ngClass]="validationMessage && validationMessage.length > 0 ?'input-error-style':'input-style'"></ion-ripple-effect>
      
      <ion-label *ngIf="!value || value.length==0 || loading" >{{field.placeholder | translate}}</ion-label>
      <ng-container *ngIf="field.ttype=='many2one' && value && !loading">
        <ion-col style="color: var(--ion-color-primary); display: flex !important; flex-direction: row; align-items: center;">
          <ion-avatar style="height: 30px; width: 30px; box-shadow: 0 0 10px #ccc;" *ngIf="haveImage">
            <img alt="https://ionicframework.com/docs/img/demos/avatar.svg" [src]="getImageSrc(value.id)" />
          </ion-avatar>
          <ion-label>{{displayName}} </ion-label>
        </ion-col>
        <ion-button fill="clear" slot="end" mode="ios" (click)="resetValue($event)">
          <ion-icon name="close-outline"></ion-icon>
        </ion-button>
      </ng-container>
      <ion-spinner *ngIf="loading"></ion-spinner>
      
      <div id="tagsScroll" class="many2many-style" (scroll)="checkOverflow()" *ngIf="(field.ttype=='many2many' || field.ttype=='one2many') && value.length>0">
        <ion-chip [ngClass]="checkType(val.id) == 'number'?'tag':'tagNew'" *ngFor="let val of value" [id]="val.id?val.id.toString():''" >
          <ion-label class="newLabel" *ngIf="checkType(val.id) != 'number'">{{'NEW'|translate}} </ion-label>
          <ion-avatar *ngIf="haveImage">
            <img alt="https://ionicframework.com/docs/img/demos/avatar.svg" [src]="getImageSrc(val)"  />
          </ion-avatar>
          <ion-label>{{val.display_name}} </ion-label>
          <ion-icon [ngClass]="checkType(val.id) == 'number'?'tag':'tagNew'" (click)="removeValue(val,$event)" name="close"></ion-icon>
        </ion-chip>
        
      </div>
      <ion-badge class="scroll-indicator"  *ngIf="noOfElements>0">
        <span> +{{noOfElements}} </span>
      </ion-badge>
    </ion-item>
    <ion-icon slot="end" class="input-icon" (click)="scanAndUpdateBarcodeList()" color="primary" *ngIf="showBarcodeScanner" name="barcode-outline"></ion-icon>
  </div>
  <ion-item (click)="openScennedElement()" class="olivery-item-for-input" style="padding-top: 5px;margin-top: 16px;" *ngIf="field && (field.ttype=='many2one' || field.ttype=='many2many' || field.ttype=='one2many') && showBarcodeScanner">{{'NUMBER_OF_SCANNED_ELEMENTS:'|translate}} {{barcodeList.length}}</ion-item>
  <div class="input-icon-wrap">
    <ion-input #ionInputEl 
    [ngClass]="{
      'input-error-style': validationMessage && validationMessage.length > 0,
      'input-style': !validationMessage || validationMessage.length === 0,
      'customized-input-1': formName === 'login_form' && !isModalOpen
      }"
      [(ngModel)]="value" 
      *ngIf="field && !field.is_location && (field.ttype === 'text' || field.ttype === 'char' || field.ttype === 'integer' || field.ttype === 'float')" 
      (ionInput)="onChangeValue($event)" 
      [inputmode]="field.ttype === 'float' ? 'decimal' : field.ttype === 'integer' ? 'numeric' : ''" 
      [type]="(field.ttype === 'float' || field.ttype === 'integer') ? 'text' : field.input_type" 
      [label]="field.placeholder | translate" 
      errorText="Invalid email" 
      [disabled]="field.readonly" 
      [labelPlacement]="'floating'" 
      fill="outline" 
      mode="md" 
      [placeholder]="field.readonly && value ? value : field.readonly ? '' : ('ENTER_YOUR' | translate) + ' ' + (field.placeholder | translate)">
    </ion-input>
    <div>
      <ion-icon slot="end" class="input-icon" (click)="scan()" color="primary" *ngIf="showBarcodeScanner" name="barcode-outline"></ion-icon>
      <ion-icon slot="end" class="input-icon" (click)="selectLocation()" color="primary" *ngIf="showLocationSelector" name="location-outline"></ion-icon>
      <ion-icon slot="end" class="input-icon" 
      color="primary" name="mic-outline" *ngIf="showVoiceToTextAbility && !isVoiceToTextInProgress"></ion-icon> 
      <ion-icon slot="end" class="input-icon"
      color="primary" name="stop-circle-outline" *ngIf="showVoiceToTextAbility && isVoiceToTextInProgress"></ion-icon>
      <ion-label style="align-self: center; margin-top: 5px;" *ngIf="isMonetary">{{recordStructureService.currencySymbol}}</ion-label>
    </div>
    <div *ngIf="showClientHistory">
      <ion-icon slot="end" class="input-icon" (click)="moveToOrderHistory()" src="../../../../assets/icon/history.svg"></ion-icon>
    </div>
  </div>
  
    
  <div  *ngIf="field && (field.ttype=='selection') || (selectionItems && selectionItems.length>0 && field.ttype=='boolean')">
      <div *ngIf="selectionItems.length>=3 && ((value && field.ttype=='selection' && !loading) || (value && value.length>0 && !loading))" class="many2one-label-container">
        <ion-label>{{field.placeholder | translate}}</ion-label>
      </div>
      <ion-item *ngIf="selectionItems.length>=3" [disabled]="loading" class="olivery-item-for-input " [ngClass]="validationMessage && validationMessage.length > 0 ?'input-error-style':'input-style'" (click)="openSelection()" button mode="ios" lines="none">
        <ion-ripple-effect *ngIf="!loading" [ngClass]="validationMessage && validationMessage.length > 0 ?'input-error-style':'input-style'"></ion-ripple-effect>
        
        <ion-label *ngIf="!value || value.length==0 || loading" >{{field.placeholder | translate}}</ion-label>
        <ng-container *ngIf="field.ttype=='selection' && value && !loading">
          <ion-col style="color: var(--ion-color-primary); display: flex !important; flex-direction: row; align-items: center;">
            <ion-label>{{value[1]}} </ion-label>
          </ion-col>
          <ion-button fill="clear" slot="end" mode="ios" (click)="resetValue($event)">
            <ion-icon name="close-outline"></ion-icon>
          </ion-button>
        </ng-container>
      </ion-item>
      <ion-item *ngIf="selectionItems.length<3" [disabled]="loading" class="selection-container" [ngClass]="validationMessage && validationMessage.length > 0 ?'input-error-style':'input-style'" mode="ios" lines="none">
        <div class="centered-div" >
          <ion-label [ngStyle]="{'color':validationMessage && validationMessage.length > 0 ?'var(--ion-color-danger)':'white'}" style=" font-weight: bold;">{{field.placeholder | translate}}</ion-label>
          <div class="buttons-container">
            <ion-button 
              *ngFor="let selectionItem of selectionItems" 
              [color]="getSelectionColor(selectionItem, value)" 
              mode="ios" 
              (click)="emitSelectionValue(selectionItem)">
                  {{ selectionItem.length >= 2 ? (selectionItem[1] | translate) : selectionItem[0] }}
            </ion-button>        
          </div>     
        </div>
      </ion-item>
  </div>

  <div (click)="setDateValue()" class="datetime-input" mode="ios"  *ngIf="field && (field.ttype=='datetime' || field.ttype=='date') && numberOfInputs > 2">
 
    <div *ngIf="value" class="date-label-container">
      <ion-label>{{field.placeholder | translate}}</ion-label>
    </div>
    <ion-label *ngIf="!value" >{{field.placeholder | translate}}</ion-label>
    <ion-label *ngIf="value" class="date-value-label">{{value}}</ion-label>
    <ion-icon name="calendar-outline"></ion-icon>
    
    <ion-modal class="ion-datetime-button-overlay" #dateModal [keepContentsMounted]="true">
      <ng-template>
        <ion-datetime  [max]="maxDateTime"  [cancelText]="'CANCEL' | translate" [doneText]="'DONE' | translate" [locale]="translate.currentLang" mode="ios"  [presentation]="field.ttype =='datetime'?'date-time':'date'" (ionChange)="onChangeValue($event)" showDefaultButtons="true" [id]="field.name">
          <span slot="time-label">{{'TIME' | translate}}</span>
        </ion-datetime>
      </ng-template>
    </ion-modal>
  
    <ion-datetime  [max]="maxDateTime"  *ngIf="numberOfInputs == 2" [cancelText]="'CANCEL' | translate" [doneText]="'DONE' | translate" [locale]="translate.currentLang" mode="ios"  [presentation]="field.ttype =='datetime'?'date-time':'date'" (ionChange)="onChangeValue($event)" showDefaultButtons="true" [id]="field.name" />
    
  </div>
  <div mode="ios"  *ngIf="field && (field.ttype=='datetime' || field.ttype=='date') && numberOfInputs == 2">
    <ion-datetime  [max]="maxDateTime" 
      *ngIf="numberOfInputs == 2"
      [cancelText]="'CANCEL' | translate"
      [doneText]="'DONE' | translate"
      [locale]="translate.currentLang"
      mode="ios"
      [presentation]="field.ttype == 'datetime' ? 'date-time' : 'date'"
      (ionChange)="onChangeValue($event)"
      [id]="field.name"
    ></ion-datetime>
  </div>
<ion-item class="boolean-style" *ngIf="field && (field.ttype=='boolean') && !selectionItems" lines="none">
  <ion-toggle [checked]="value" (ionChange)="onChangeValue($event)" style="padding: 0; " mode="ios">{{field.placeholder | translate}}</ion-toggle>
</ion-item>

<ion-row *ngIf="field && field.ttype=='binary' && !field.is_signature ">
  <img (click)="openCamera()"
    style="border-radius: 4px; margin: 0 auto; width: 173px; border:1px solid #3D3937;"
     alt="" [src]="(value && !value.includes('base64')?'data:image/png;base64,':'')+ (value || '../assets/photo.PNG')" />
</ion-row>

<ion-row *ngIf="field && field.is_signature ">
  <ion-col size="12">
    <canvas #sPad width="300" height="150" style="border: 1px solid black;"></canvas>
  </ion-col>
  <ion-col size="12" class="ion-text-center" >
    <ion-button (click)="clearSignature()">{{'CLEAR' | translate}}</ion-button>
  </ion-col>
</ion-row>

<ion-row *ngIf="field && field.is_multi_scan ">
  <ion-item [disabled]="loading" class="olivery-item-for-input " [ngStyle]="{'border':formInputs?'none':''}" [ngClass]="validationMessage && validationMessage.length > 0 ?'input-error-style':'input-style'" (click)="openBarcodeScanner()" button mode="ios" lines="none">
    <ion-label *ngIf="!value || value.length==0 || loading" >{{field.placeholder | translate}}</ion-label>
    <ion-ripple-effect *ngIf="!loading" [ngClass]="validationMessage && validationMessage.length > 0 ?'input-error-style':'input-style'"></ion-ripple-effect>
    <div id="tagsScroll" class="many2many-style" (scroll)="checkOverflow()" *ngIf="value.length>0">
      <ion-chip [ngClass]="checkType(val.id) == 'number'?'tag':'tagNew'" *ngFor="let val of value" [id]="val" >
        <ion-label>{{val}} </ion-label>
        <ion-icon class="tag" (click)="removeSequence(val,$event)" name="close"></ion-icon>
      </ion-chip>
      
    </div>
    <ion-badge class="scroll-indicator"  *ngIf="value.length>0">
      <span> +{{value.length}} </span>
    </ion-badge>
  </ion-item>
</ion-row>

<ion-row *ngIf="attachments && attachments.length > 0">
  <ion-col size="6" *ngFor="let image of attachments; let i = index" style="padding: 8px;">
    <ion-thumbnail style="display: flex; justify-content: center; align-items: center; overflow: hidden;">
      <img [src]="image" style="width: 100%; height: auto; max-height: 200px;">
      <ion-icon name="close-circle" class="remove-icon" (click)="removeImage(i)" style="position: absolute; top: 8px; right: 8px; color: red; cursor: pointer; font-size: 24px;"></ion-icon>
    </ion-thumbnail>
  </ion-col>
</ion-row>
<app-form-creator [ttype]="field.ttype"  (formOutputs)= "checkFormOutputs($event)" [isIsolated]="true" [destroyed]="destroyed" *ngIf="(formInputs && field.ttype != 'many2one') || (formInputs && field.ttype == 'many2one' && !value) && !loading" [model]="formInputs.model" [formInputs]="formInputs.inputs" [steps]="formInputs.steps"></app-form-creator>
</ion-col>

<ion-col class="ion-no-padding" *ngIf="isButton" >
  <ion-button [color]="success?'success':fail?'danger':background?background:'primary'" style="color: #fff !important; border: 1px solid var(--ion-color-primary);" [disabled]="loading" [fill]="'solid'"   [ngClass]="{ 'login_button': formName === 'login_form' }"  class="button-style" (click)="emitFunction(buttonFunction)" mode="ios" >
    <ion-icon *ngIf="buttonIcon" [name]="buttonIcon"></ion-icon>
    <ion-label *ngIf="buttonText">{{buttonText | translate}}</ion-label>
    
    <ion-spinner slot="end" *ngIf="loading" [color]="success?'success':fail?'danger':buttonFunction=='getCurrentLocation'?'primary':'light'" style="zoom: .8;"></ion-spinner>
  </ion-button>
</ion-col>

<ion-col class="ion-no-padding" style="padding-top: 8px;" *ngIf="isSeparator && separatorTitle" >
  <ion-item-divider class="separatorDivider" [ngStyle]="{'border-top':firstSeparator?'0':'2px solid','padding-top':firstSeparator?'0':'16px'}"  mode="ios">
    <div >{{separatorTitle | translate}}</div>
  </ion-item-divider>
</ion-col>
<ng-container *ngFor="let error of validationMessage">
  <div *ngIf="!field.is_location" class="validation"><ion-icon name="information-circle"></ion-icon>{{error.messageKey | translate:error.messageParams}}</div>
</ng-container>


