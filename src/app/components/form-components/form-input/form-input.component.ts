import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { AlertController, IonInput, IonModal, ModalController, ModalOptions, ToastController } from '@ionic/angular';
import { Subject, debounceTime, fromEvent, of } from 'rxjs';
import { RecordCreatorPage } from 'src/app/modals/record-creator/record-creator.page';
import { OdooJsonRPC } from 'src/app/services/odooJsonRPC';
import { environment } from 'src/environments/environment';
import { DynamicSelectionComponent } from '../../dynamic-selection/dynamic-selection.component';
import { TranslateService } from '@ngx-translate/core';
import SignaturePad from 'signature_pad';
import { Geolocation } from '@capacitor/geolocation'
import { RecordStructureService } from 'src/app/services/record-structure-service';
import { Camera, CameraResultType, CameraSource } from '@capacitor/camera';
import { BarcodeScanComponent } from 'src/app/modals/barcode-scan/barcode-scan.component';
import { NativeSettings, AndroidSettings, IOSSettings } from 'capacitor-native-settings';
import { DatePipe } from '@angular/common';
import { SingleSelectionModalPage } from 'src/app/modals/list-selectors/single-selection-modal/single-selection-modal.page';
import { OpenScannedElementsComponent } from '../../open-scanned-elements/open-scanned-elements.component';
import { Router } from '@angular/router';
import { OrdersPage } from 'src/app/pages/orders/orders.page';
import { dialogService } from 'src/app/services/error-handlar-service';
import { LocationSelectorComponent } from 'src/app/modals/location-selector/location-selector.component';
import { Keyboard } from '@capacitor/keyboard';
import * as moment from 'moment';



@Component({
  selector: 'app-form-input',
  templateUrl: './form-input.component.html',
  styleUrls: ['./form-input.component.scss'],
})
export class FormInputComponent implements OnInit {
  @ViewChild('dateModal', {}) dateModal: IonModal | undefined
  @ViewChild('sPad', { static: false }) signaturePadElement:any;
  @ViewChild('ionInputEl', {}) ionInputEl: IonInput | undefined;
  
  signaturePad: any;
  @Input() searchBy!: string[]
  @Input() isSeparator!: boolean
  @Input() separatorTitle!: string
  @Input() background!: string
  @Input() color!: string
  @Input() isButton!:boolean
  @Input() buttonText!:string
  @Input() buttonIcon!:string
  @Input() buttonFunction!:string
  @Input() field!: { id:number,ttype: string; name: string;placeholder:string; model_name: string; input_type: string;readonly:boolean;is_location:boolean;is_signature:boolean;barcode_verified:boolean;is_multi_scan:boolean;cascade_mapping_fields:boolean}
  @Input() value!: any
  @Input() validations!:{email:boolean; required: boolean; max_length: number;min_length: number}
  @Input() invisible!: boolean
  @Input() parentField!: { ttype: string; name: string; model_name: string;field_map:string,reflect_to_parent:string,cascade_child_to_parent:boolean }
  @Input() haveImage!: boolean
  @Input() imageSource!: CameraSource
  @Input() imageFieldName!:string
  @Input() domain!: any
  @Input() searchDomain!: string[]
  @Input() limitPerSearch!: number;
  @Input() showBarcodeScanner! : boolean
  @Input() showLocationSelector! : boolean
  @Input() showVoiceToTextAbility:boolean = false;
  @Input() parentValue!: any
  @Input() loading:string='false'
  @Input() success:string='false'
  @Input() fail:string='false'
  @Output() selectedValue: any = new EventEmitter<any>();
  @Output() onLocationSet: any = new EventEmitter<any>();
  @Output() selectedParentValue: any = new EventEmitter<any>();
  @Input() isFormCreator : boolean = false;
  @Input() isMonetary:boolean = false
  @Input() formName! : string
  @Input() firstSeparator!:boolean
  @Input() selectionItems!: any[];
  @Output()  buttonPress: any =new EventEmitter <any> ();
  @Output() setParentLoading: any = new EventEmitter<any>();
  @Output() updateMappingFields: any = new EventEmitter<any>();
  @Input() isAutoFillMappingFieldsEnabled! :boolean
  @Input() connected_field_to_selection_modal!:any
  @Input() search_domain_inside_selection_modal!:string[]
  @Input() showClientHistory!:boolean
  @Input() formInputs:any
  @Input() statusId!:number
  @Input() numberOfInputs!:number
  @Input() destroyed!: Subject<any>;
  @Input() isModalOpen!: boolean
  selectedDateTime! : string
  env = environment
  displayName!: string
  noOfElements!: number;
  isVoiceToTextInProgress:boolean =false
  voiceToTextResults!: String[];
  @Input() validationMessage!:any[]
  clientMatched: boolean = false
  clients: any[] = [];
  @Input() mappingFields!: any[];
  barcodeList:string[] = []
  referenceBarcodeList:string[] = []
  sequenceBarcodeList:string[] = []
  validBarcodes:string[] = []
  attachments:any[] = [];
  maxDateTime: string='';
  keyboardIsOpen = false;
  constructor(
    private modalCtrl: ModalController,
    private odooRpc:OdooJsonRPC,
    public translate: TranslateService,
    private alertController:AlertController,
    private changeDetectorRef: ChangeDetectorRef,
    private toastCrtl : ToastController,
    public recordStructureService:RecordStructureService,
    private datePipe: DatePipe,
    private router : Router,
    private dialogService: dialogService

  ) { 
    Keyboard.addListener('keyboardDidShow', () => {
      this.keyboardIsOpen = true;
    });
  
    Keyboard.addListener('keyboardDidHide', () => {
      this.keyboardIsOpen = false;
    });
  }



  
  ngAfterViewInit() {
    if (this.signaturePadElement) {
        this.signaturePad = new SignaturePad(this.signaturePadElement.nativeElement);
        this.signaturePadElement.nativeElement.addEventListener('touchend', () => {
          this.saveSignature()
      });
  
    }
    if (this.ionInputEl) {
      const nativeElement = this.ionInputEl.getInputElement().then(inputEl => {
        fromEvent(inputEl, 'click')
          .pipe(debounceTime(50))
          .subscribe(() => this.checkOpenSelection());
      });
    }

    this.parseDomain()
}

  parseDomain() {
    if (this.domain == undefined)
      return
    
    for (let i = 0; i < this.domain.length; i++) {
      if (this.domain[i][2] && this.domain[i][2].startsWith('{{this.')) {
        const extractValue = (val: string) => val.substring(2, val.length - 2);
        let val = extractValue(this.domain[i][2]);
        val = val.replace("this.", "");
        const key = val as keyof FormInputComponent;
        if (this.domain[i][1] == 'in' || this.domain[i][1] == 'not in') {
          if (key in this) {
            this.domain[i][2] = [this[key]];
          } else {
            console.error(`Property ${val} does not exist on FormInputComponent`);
          }
        }
      }
    }
  }



  saveSignature() {
    if (this.signaturePad) {
        const signatureDataURL = this.signaturePad.toDataURL(); 
        this.emitValue(signatureDataURL);
    }
  }

  clearSignature() {
    if (this.signaturePad) {
        this.signaturePad.clear();
        this.emitValue(false);
    }
  }

  ngOnInit() {
    if (this.field&&(this.field.ttype=="date"||this.field.ttype=="datetime")){
      this.maxDateTime = moment().add(1, 'year').endOf('year').format('YYYY-MM-DD');
    }

    if (this.field && this.field.is_location) {
      this.field.name="get.current.location"
      this.locationSetup(this.validations);
    }
    else if (this.field && this.field.is_signature) {
      this.field.name="signature"
    }
    else if (this.field && this.field.is_multi_scan) {
      this.field.name="sequences"
    }
  }

  async locationSetup(validation :any) {
    try{
      let coords: any = {}; 
      let permissions = await Geolocation.checkPermissions();
      if(permissions.coarseLocation != 'granted' || permissions.location != 'granted'){        
        permissions = await Geolocation.requestPermissions({permissions:['coarseLocation','location']});
      }
      if(validation.required){
        if(permissions.coarseLocation != 'granted' || permissions.location != 'granted'){
          this.presentSettingsLocationAlert(validation)
        }
      }
      else{
        Geolocation.getCurrentPosition().then((response) => {
          if(response && response.coords && response.coords.latitude && response.coords.longitude) {
            coords['latitude'] = response.coords.latitude;
            coords['longitude'] = response.coords.longitude;
            this.emitValue(coords);
          } else {
            this.presentSettingsLocationAlert(validation)
          }
        }).catch((error) => {
          this.presentSettingsLocationAlert(validation)
        });
      }
    }
    catch{
      this.presentSettingsLocationAlert(validation)
    }
  }

  async onFocus() {
    if (this.keyboardIsOpen) {
      document.getElementById(this.field.id.toString())?.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
        inline: 'nearest',
      });
    } else {
      const listener = await Keyboard.addListener('keyboardDidShow', () => {
        document.getElementById(this.field.id.toString())?.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
          inline: 'nearest',
        });
        listener.remove();
      });
    }
  }
  
  presentSettingsLocationAlert(validation : any) {
    this.alertController.create({
      message:`
      <div>
        <p>${this.translate.instant('PLEASE_ENABLE_LOCATION_ACCESS')}</p>
        <p><strong>${this.translate.instant('ENABLE_LOCATION')}:</p>
        <p></strong>${this.translate.instant('FIRST_MESSAGE_ABOUT_ENABLING_LOCATION_ON_THE_DEVICE')}</p>
        <p><strong>${this.translate.instant('ENABLE_APP_PERMISSION')}:</p>
        <p></strong>${this.translate.instant('SECOND_MESSAGE_ABOUT_ENABLING_APP_PERMISSION')}</p>
      </div>
    `,
      mode:'ios',
      buttons:[
        {
          text:this.translate.instant("OPEN_SETTINGS"),
          handler:()=>{
            NativeSettings.open({
              optionAndroid: AndroidSettings.ApplicationDetails, 
              optionIOS: IOSSettings.App
            }).then(()=>{
              this.locationSetup(this.field)
            })
          }
        },
        {
          text:this.translate.instant("CANCEL"),
          handler:()=>{
            this.presentToast(this.translate.instant("CANT_ACCESS_LOCATION"),this.translate.instant("CAN_NOT_ACCESS_LOCATION_YOU_NEED_TO_ENABLE_YOUR_LOCATION") );
          if(validation.required){
            this.emitValue(undefined);
            this.modalCtrl.dismiss()
            }
          }
        }
      ]
    }).then(alert=>{
      alert.present()
    })
  }
  
  
  async presentToast(title: string, message: string) {
    const toast = await this.toastCrtl.create({
      header: title,
      message: message,
      duration: 5000,
      position: 'top'
    });
    toast.present();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if(changes['value'] && changes['value']['currentValue'] && (typeof changes['value']['currentValue']) == 'object' && 'display_name' in changes['value']['currentValue']){
      this.displayName = changes['value']['currentValue']['display_name'] as string
      if(this.parentField && !this.selectionItems){
        
        this.searchForParent(changes['value']['currentValue'])
      }
      else if (this.parentField){
        this.searchForParentLocal(changes['value']['currentValue'])
      }
    }
  }
  searchForParentLocal(childValue: any) {
    let parentValue = childValue[this.parentField.field_map]
    this.emitParentValue({id:parentValue[0], display_name:parentValue[1]})
  }

  emitParentValue(parentValue:any) {
      this.selectedParentValue.emit(parentValue)
      this.setParentLoading.emit(false)
  }

  searchForParent(childValue:any) {
    
    
    
    if(childValue[this.parentField.field_map]){
      setTimeout(()=>{
        this.setParentLoading.emit(true)
      },1)
      
      return of(this.odooRpc.searchRead(this.parentField.model_name,[['id','=',childValue[this.parentField.field_map][0]]],false,0,0,"id DESC").then(data=>{
        
        try{
          let parentValue = data.body.result.result[0]
          if(parentValue)
            this.emitParentValue(parentValue)
          else
            this.setParentLoading.emit(false)
        }
        catch{
          this.setParentLoading.emit(false)
        }
      }))
    }
    return false
    

  }



  getDomain() {
    if (this.parentField && this.parentValue)
      return [[this.parentField.field_map, '=', this.parentValue]].concat(this.domain || [])
    else return this.domain
  }

  /**
   * Opens a modal with the appropriate selector based on the input type.
   */
  openSelector() {
    
    // if(this.formInputs){
    //   return
    // }
    this.openModalValueSelector(this.field.ttype);
  }
  
  openModalValueSelector(ttype: string) {
    let options: ModalOptions = {
      component: DynamicSelectionComponent,
      initialBreakpoint : 0.65,
      breakpoints :[0, 0.25, 0.5, 0.75,1],
      handleBehavior : 'cycle',
      componentProps: {
        modelName: this.field.model_name,
        selectionItems: this.selectionItems,
        domain: this.getDomain(),
        selectedValue: this.value,
        searchDomain: this.searchDomain ? this.searchDomain : [['name','ilike','value']],
        limitPerSearch:this.limitPerSearch || 0,
        parentField: this.parentField,
        selectionType: ttype ,
        placeholder:this.field.placeholder,
        imageFieldName:this.imageFieldName,
        isAutoFillMappingFieldsEnabled : this.isAutoFillMappingFieldsEnabled,
        mappingFields : this.mappingFields
      }
    };
    this.modalCtrl.create(options).then(modal => {
      modal.present();
      modal.onDidDismiss().then(output => {
        if(output && output.data){
          this.emitValue(output.data);
          if(this.isAutoFillMappingFieldsEnabled){
            this.updateMappingFields.emit(output.data,this.field)
          }
          if (ttype == "many2many" || ttype == "one2many" )  {
            setTimeout(() => {
              this.checkOverflow();
            }, 100);
          }
        }
        
      });
    });
  }

  async openBarcodeScanner() {
    let scanResult:any = await new Promise((resolve, reject) => {
      this.modalCtrl.create({
        component: BarcodeScanComponent,
        backdropDismiss:false,
        componentProps:{
          typeSelectionItems:['SEQUENCE']
        },
        cssClass:"exclude-hide"
      }).then(modal => {
        modal.present();
        modal.onDidDismiss().then(scanResult => {
          resolve(scanResult);
        })
      })
    });
    if(!this.value){
      this.value=[]
    }
    if(scanResult.data.scannedValue){
      if(this.value.includes(scanResult.data.scannedValue)){
        const toast = await this.toastCrtl.create({
          header: this.translate.instant('DUPLICATE_BARCODE'),
          message: this.translate.instant('THIS_BARCODE_ALREADY_SCANNED'),
          duration: 5000,
          position: 'top'
        });
        toast.present();
      }else{
        this.value.push(scanResult.data.scannedValue)
        this.emitValue(this.value)
        this.openBarcodeScanner()
      }
    }
    setTimeout(() => {
      this.checkOverflow();
    }, 100);
  }


  openSelection() {
    let options: ModalOptions = {
      component: SingleSelectionModalPage,
      initialBreakpoint : 0.65,
      breakpoints :[0, 0.25, 0.5, 0.75,1],
      handleBehavior : 'cycle',
      componentProps: {
        selectionItems:this.selectionItems,
        title : this.field.placeholder,
        selectedValue : this.value
      }
    };
    this.modalCtrl.create(options).then(modal => {
      modal.present();
      modal.onDidDismiss().then(output => {
        if(output && output.data){
          this.emitValue(output.data);
      }}
      )
    });
  }

  emitValue(value: any) {
    if(typeof value == "object" && 'display_name' in value){
      this.displayName = value['display_name']
    }
    else{
      if(Array.isArray(value) && value.length > 0 && value[0].multiScannedElemtns && this.value){
        const sequencesToFilter: Set<string> = new Set(value[0].sequences);
        this.value = this.value.filter((item: { sequence: string; }) => sequencesToFilter.has(item.sequence));
      }
      else{
        this.value = value
      }
    }
    if(Array.isArray(value) && value.length > 0){
      let inputBarcode :string[] = []
      for(let item of value){
        if(item.sequence){
          inputBarcode.push(item.sequence)
        }
      }
      this.barcodeList = this.barcodeList.concat(inputBarcode)
      this.noOfElements = this.barcodeList.length
    }
    
    this.selectedValue.emit(value)
  }

  emitFunction(functionName:string){
    this.buttonPress.emit(functionName)
  }

  onChangeValue(event: any) {
    let value = event.detail.value;
    if(this.field.ttype=='float' || this.field.ttype=='integer'){
      value = value.replace(',', '.');
      value = value.replace('٫', '.');
      value = value.replace(/[^\d٠١٢٣٤٥٦٧٨٩.]+/g, ''); 
      const dotCount = value.split('.').length - 1;
      if (dotCount > (this.field.ttype=='integer'?0:1)) {
        const parts = value.split('.');
        value = parts[0] + '.' + parts.slice(1).join('');
      }
      if(value == '.'){
        value = ''
      }

    }
    else if (this.field.ttype == 'boolean') {
      value = event.detail.checked;
    } else if (this.field.ttype == 'datetime' || this.field.ttype == 'date') {
      // Format the date
      value = this.datePipe.transform(value, 'yyyy-MM-dd HH:mm:ss');
    }
    if(this.field && this.field.input_type == 'tel'){
      const input = event.target as HTMLInputElement;
      const cleanValue = input.value.replace(/[^\d\u0660-\u0669+]/g, '');
      if (input.value !== cleanValue) {
        input.value = cleanValue;
        event.target.setSelectionRange(cleanValue.length, cleanValue.length);
      }
    }
    if(this.ionInputEl && this.ionInputEl.value){
      this.ionInputEl.value = value
    }
  
    this.emitValue(value);
  }

  checkOpenSelection(){
    if(!this.connected_field_to_selection_modal){
      return
    }
    let options: ModalOptions = {
      component: DynamicSelectionComponent,
      initialBreakpoint : 0.65,
      breakpoints :[0, 0.25, 0.5, 0.75,1],
      handleBehavior : 'cycle',
      componentProps: {
        modelName: this.connected_field_to_selection_modal[0].relation,
        selectionItems: this.selectionItems,
        selectedValue: this.value,
        searchDomain: this.search_domain_inside_selection_modal ? this.search_domain_inside_selection_modal : [['name','ilike','value']],
        limitPerSearch:this.limitPerSearch || 0,
        parentField: this.parentField,
        selectionType: this.connected_field_to_selection_modal[0].ttype ,
        placeholder:this.connected_field_to_selection_modal[0].name,
        imageFieldName:this.imageFieldName,
        isAutoFillMappingFieldsEnabled : this.isAutoFillMappingFieldsEnabled,
        mappingFields : this.mappingFields,
        fieldType: this.field.input_type
      }
    };
    this.modalCtrl.create(options).then(modal => {
      modal.present();
      modal.onDidDismiss().then(output => {
        if(output && output.data){
            this.updateMappingFields.emit(output.data,this.field)
            this.emitValue(output.data);
        }
        
      });
    });
  }
  

  setDateValue(){
    this.dateModal?.present()    
  }

  resetValue(event:Event){
    event.stopPropagation()
    this.emitValue(undefined)
    if (this.parentField && this.parentField.cascade_child_to_parent) {
      this.emitParentValue(false)
    }
    if(this.isAutoFillMappingFieldsEnabled && this.field.cascade_mapping_fields){
      this.updateMappingFields.emit(false,this.field)
    }
  }

  removeValue(value:any,event:Event){
    event.stopPropagation()
    this.value = this.value.filter((val:any)=>val.id != value.id)
    setTimeout(()=>{
      this.checkOverflow()
    },100)
    this.emitValue(this.value)
  }

  removeSequence(value:any,event:Event){
    event.stopPropagation()
    this.value = this.value.filter((val:any)=>val != value)
    setTimeout(()=>{
      this.checkOverflow()
    },100)
    this.emitValue(this.value)
  }

  checkOverflow () {
    let tagsScrollElement = document.getElementById('tagsScroll')
    let elementIds:string[] = []
    for(let val of this.value){
      elementIds.push(val.id.toString())
    }
    let noOfElements=0
    let totalOffset = 0
    for(let elementId of elementIds){
      
      let element = document.getElementById(elementId)
      
      if(element && element.offsetWidth){
        totalOffset+=element.offsetWidth
      }

      if(document.documentElement.dir == 'ltr' &&tagsScrollElement && totalOffset > tagsScrollElement.scrollLeft + tagsScrollElement.clientWidth) {
        noOfElements+=1
      }
      else if(document.documentElement.dir == 'rtl' &&tagsScrollElement && totalOffset < tagsScrollElement.scrollLeft + tagsScrollElement.clientWidth) {
        noOfElements+=1
      }
    }
    this.noOfElements = noOfElements
    
  }

  openRecordForm(){
    
    this.modalCtrl.create({
      component : RecordCreatorPage,
      componentProps:{
        formName : this.formName,
        field : this.field
      }
    }).then(modal =>{
      modal.present()
    })
  }

  getImageSrc(value: any): string {
    
    if(typeof value.id == 'string'){
      return 'data:image/*;base64,'+value[this.imageFieldName]
    }
    return `${this.env.url}/web/image/${this.field.model_name}/${value.id}/${this.imageFieldName}/40x40`;

  }
  
  async selectLocation(): Promise<void> {
    this.modalCtrl.create({
      component:LocationSelectorComponent,
      backdropDismiss:false,
    }).then(modal=>{
      modal.present()
      modal.onDidDismiss().then(locationResult=>{
        
        if(locationResult && locationResult.data){
          this.emitValue(locationResult.data.formattedAddress)
          this.emitLocationValues({latitude:locationResult.data.latitude,longitude:locationResult.data.longitude})
        }
      })
    })
    
  }

  emitLocationValues(latLng:any){
    this.onLocationSet.emit(latLng)
  }

  async scan(): Promise<void> {
    this.modalCtrl.create({
      component:BarcodeScanComponent,
      backdropDismiss:false,
      cssClass:"exclude-hide"
    }).then(modal=>{
      modal.present()
      modal.onDidDismiss().then(scanResult=>{
        if(scanResult && scanResult.data && scanResult.data.scanningFinished){
          this.emitValue(false)
        } else if(scanResult && scanResult.data && scanResult.data.scannedValue){
          this.emitValue(scanResult.data.scannedValue)
        } else if(scanResult && scanResult.data){
          this.emitValue(scanResult.data)
        }
      })
    })
    
  }

  emitSelectionValue(selectionItem:any){
    (typeof selectionItem[0] === 'boolean') ? this.emitValue(selectionItem[0]) : this.emitValue(selectionItem)
  }

  checkType(value:any){
    return typeof value
  }

  getSelectionColor(selectionItem:any,value:any){
    return (value[0] === selectionItem[0] || (typeof selectionItem[0] === 'boolean' && value === selectionItem[0])) ? 'primary' : 'light'
  }

  async scanAndUpdateBarcodeList(){
    this.scanMulti().then(async (scanResult : any)=>{
    if(scanResult.data.scanningFinished){
      this.emitValue([{'multiScannedElemtns':true,'sequences':this.barcodeList}])
    }
    else if(scanResult.data && !this.barcodeList.includes(scanResult.data.scannedValue)){
        this.validBarcodes.push(scanResult.data.scannedValue as string)
        this.barcodeList.push(scanResult.data.scannedValue as string)
        this.scanAndUpdateBarcodeList()
      }
      else if(scanResult.data){
        this.dialogService.warning({
          input: this.translate.instant('BARCODE_ALREADY_SCANNED'),
          message: this.translate.instant('BARCODE_ALREADY_SCANNED'),
          whatToDo: this.translate.instant('PLEASE_SCAN_OTHER_BARCODE'),
          code: '1310'
        })
      }
    })
  }

  scanMulti(){
    return new Promise((resolve, reject) => {
      this.modalCtrl.create({
        component: BarcodeScanComponent,
        backdropDismiss:false,
        componentProps:{
          typeSelectionItems:['SEQUENCE','REFERENCE']
        },
        cssClass:"exclude-hide"
      }).then(modal => {
        modal.present();
        modal.onDidDismiss().then(scanResult => {
          console.log('*********************************************')
          console.log(scanResult)
          resolve(scanResult);
        })
      })
    });
  }

  // TODO: move it to general place and use it in every place in the project
  async showAlert(headerTextKey: string, messageTextKey: string, buttonsTextsKeys: string[]): Promise<void> {
    const alert = await this.alertController.create({
      header: this.translate.instant(headerTextKey),
      message: this.translate.instant(messageTextKey),
      buttons: buttonsTextsKeys.map(buttonTextKey => this.translate.instant(buttonTextKey))
    });
    await alert.present();
  }

  // TODO: !if neeeded!: get languages code from client config and verify if it's supported, and maybe I need to get default lang from config if no current lang
  async getLanguageCode(language: string): Promise<string> {
    switch(language){
      case "ar":
        return "ar-AE";
      case "en":
        return "en-US"
      default:
        return "ar-AE"
    }
  }

  // async startListenToConvertVoiceToText(): Promise<void> {

  //   await SpeechRecognition.requestPermissions();
  //   const { available } = await SpeechRecognition.available();

  //   if( !available ){
  //     await this.showAlert('SPEECH_RECOGNITION_NOT_AVAILABLE', 'PLEASE_BE_SURE_YOU_HAVE_SPEECH_RECOGNITION', ['OK']);
  //     return;
  //   } 

  //   const currentLanguageCode = await this.getLanguageCode(this.translate.currentLang);
  //   SpeechRecognition.start({
  //     language: currentLanguageCode,
  //     partialResults: true,
  //     popup: false,
  //   });
  //   SpeechRecognition.addListener("partialResults", (voiceAsText: any) => {
  //     if(voiceAsText.matches && voiceAsText.matches.length > 0 ){
  //       const voiceAsTextValue = this.field.input_type == "tel" ? voiceAsText.matches[0].replace(/ /g, "") : voiceAsText.matches[0];
  //       this.emitValue(voiceAsTextValue);
  //       this.changeDetectorRef.detectChanges();
  //     }
  //   });
  //   this.isVoiceToTextInProgress = true;
  // }
  
  // async stopListenAndConvertVoiceToText(): Promise<void> {

  //   SpeechRecognition.stop();
  //   SpeechRecognition.removeAllListeners();
  //   this.isVoiceToTextInProgress = false;
  // }

  async openCamera() {
    try {
      
        const image = await Camera.getPhoto({
            quality: 90,
            allowEditing: false,
            resultType: CameraResultType.DataUrl,
            source:this.imageSource || CameraSource.Prompt,
            promptLabelPicture:this.translate.instant('OPEN_CAMERA'),
            promptLabelPhoto:this.translate.instant('OPEN_GALLERY'),
            promptLabelHeader:this.translate.instant('CHOOSE')
        });
        
        if (image && image.dataUrl) {
            this.value = image.dataUrl.split('base64,')[1]
            this.emitValue(image.dataUrl.split('base64,')[1]);
        }
    } catch (error) {
        console.error('Error capturing image:', error);
    }
}
removeImage(index?: number) {
  if (index||index==0){
    this.attachments.splice(index, 1);
  }
  else{
    this.attachments=[]
  }
}


openScennedElement(){  
  this.modalCtrl.create({
    component: OpenScannedElementsComponent,
    backdropDismiss:false,
    componentProps:{
      barcodeList:this.barcodeList
    },
    cssClass:"hide-when-scan-barcode",
  }).then(modal => {
    modal.present();
    modal.onDidDismiss().then(scanResult => {
      const sequencesToFilter: Set<string> = new Set(scanResult.data.barcodeList);
      this.barcodeList = this.barcodeList.filter((item) => sequencesToFilter.has(item));
      this.emitValue([{'multiScannedElemtns':true,'sequences':scanResult.data.barcodeList}])
    })
  })
}

checkFormOutputs(event:any){
  
  if(event.formVals){
    if(this.field.ttype=='many2one'){
      this.emitValue(event.formVals)
    }
    else{
      let v:any[] = (this.value || [])
      v.push(event.formVals)
      this.emitValue(v)
    }
  }
}

moveToOrderHistory(){
  let domain = []
  if(this.field && this.field.name){
    domain.push([this.field.name,'=',this.value])
  }
  else{
    domain.push(['customer_mobile','=',this.value])
  }
  this.modalCtrl.create({
    component:OrdersPage,
    initialBreakpoint : 0.65,
    breakpoints :[0, 0.25, 0.5, 0.75,1],
    handleBehavior : 'cycle',
    mode:'ios',
    componentProps:{
      domain: domain
    }
  }).then(modal =>{
    modal.present()
  })
}

}
