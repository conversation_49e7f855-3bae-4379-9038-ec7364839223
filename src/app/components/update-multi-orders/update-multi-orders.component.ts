import { Component, OnInit } from '@angular/core';
import { ModalController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { debounceTime, Subject } from 'rxjs';
import { BarcodeScanComponent } from 'src/app/modals/barcode-scan/barcode-scan.component';
import { dialogService } from 'src/app/services/error-handlar-service';
import { OdooJsonRPC } from 'src/app/services/odooJsonRPC';
import { FollowerOrderScannerComponent } from '../follower-order-scan/follower-order-scanner.component';
import { FollowOrderService } from 'src/app/services/follow-orders-service';

@Component({
  selector: 'app-update-multi-orders',
  templateUrl: './update-multi-orders.component.html',
  styleUrls: ['./update-multi-orders.component.scss'],
})
export class UpdateMultiOrdersComponent  implements OnInit {

  state!:any
  expandedItems:string[] = []
  barcodeList:string[] = []
  referenceBarcodeList:string[] = []
  sequenceBarcodeList:string[] = []
  validBarcodes:string[] = []
  firstScanType!:string

  messages:any = {}

  disabled: boolean = false
  barcodesToFetch: any[] = []
  statuses: any = {}
  orderInfo: any = {}

  private updateInputsSubject = new Subject<{ }>();

  constructor(
    private modalCtrl:ModalController,
    private translate:TranslateService,
    private dialogService: dialogService,
    private odooRpc: OdooJsonRPC,
    private followOrdersService: FollowOrderService

  ) { }

  ngOnInit() {
    if(this.firstScanType== 'REFERENCE'){
      this.referenceBarcodeList.push(this.barcodeList[0])
    }
    else if(this.firstScanType== 'SEQUENCE'){
      this.sequenceBarcodeList.push(this.barcodeList[0])
    }
    this.validBarcodes.push(this.barcodeList[0])
    this.barcodesToFetch.push(this.barcodeList[0])
    this.setupInputUpdateStream()
    this.updateInputsSubject.next({})
  }

   async updateState(){
    this.disabled = true
    let result = await this.odooRpc.call('rb_delivery.order', 'bulk_change_state_by_barcode', [this.state,{'reference_id':this.referenceBarcodeList,'sequence':this.sequenceBarcodeList}])
    this.disabled = false
    if (result && result.body && result.body.result && result.body.result.result && result.body.result.result.length > 0) {
      let err_messages = result.body.result.result
      for (let msg of err_messages) {
        if(msg.reference_id) {
          this.messages[msg.reference_id] = msg.message
        }
        this.messages[msg.sequence] = msg.message
      }
    } else {
      if (result && result.body && result.body.error && result.body.error.data && result.body.error.data.message) {
        this.dialogService.warning({
          input: result.body.error.data.message,
          message: result.body.error.data.message,
        })
      } else {
        this.modalCtrl.dismiss()
      }
    }

  }


  async scanAndUpdateBarcodeList(){
    this.openBarcodeScanner().then( async scanResult=>{
      if(scanResult.data.scanningFinished){
        return
      }
      for (let barcode of scanResult.data.scannedValues) {
        if(barcode && !this.barcodeList.includes(barcode)){

          this.barcodesToFetch.push(barcode)
          this.updateInputsSubject.next({})
          let status = ''
          if (typeof this.state == 'object') {
            status = this.state.name
          } else {
            status = this.state
          }
        let goIntoFollowOrdersScan = await this.followOrdersService.validateIfShouldScanFollowOrders(status)
        let follower_obj = await this.followOrdersService.getFollowSequences(barcode)

          if(follower_obj && follower_obj.length > 0 && goIntoFollowOrdersScan){
            const names = follower_obj.map((item: { name: string }) => item.name);
            const barcodes = follower_obj.map((item: { follow_up_sequence: string }) => item.follow_up_sequence);
            this.modalCtrl.create({
              component : FollowerOrderScannerComponent,
              cssClass:"hide-when-scan-barcode",
              componentProps :{
                change : false,
                originalSequence : barcode,
                sequenceBarcodeList : barcodes,
                nameList: names,
              }}).then(modal =>{
                modal.present()

                modal.onDidDismiss().then(output => {
                  console.log(output)
                  if(scanResult.data.type == 'REFERENCE'){
                    this.referenceBarcodeList.push(barcode as string)
                    this.firstScanType = 'REFERENCE'
                  }
                  else if(scanResult.data.type == 'SEQUENCE'){
                    this.sequenceBarcodeList.push(barcode as string)
                    this.firstScanType = 'SEQUENCE'
                  }
          
          
                  this.validBarcodes.push(barcode as string)
                  this.barcodeList.push(barcode as string)
                  
                })
              })
          } else {
            if(scanResult.data.type == 'REFERENCE'){
              this.referenceBarcodeList.push(barcode as string)
            }
            else if(scanResult.data.type == 'SEQUENCE'){
              this.sequenceBarcodeList.push(barcode as string)
            }


            this.validBarcodes.push(barcode as string)
            this.barcodeList.push(barcode as string)
            
          }
        }
      }
    })
  }


  openBarcodeScanner(): Promise<any> {
    return new Promise((resolve, reject) => {
      this.modalCtrl.create({
        component: BarcodeScanComponent,
        backdropDismiss:false,
        componentProps:{
          typeSelectionItems:['SEQUENCE','REFERENCE'],
          selectedType: this.firstScanType,
          mode:'multi',
          orders:this.orderInfo
        },
        cssClass:"exclude-hide"
      }).then(modal => {
        modal.present();
        modal.onDidDismiss().then(scanResult => {
          this.firstScanType = scanResult.data.type
          resolve(scanResult);
        })
      })
    });
  }

  setupInputUpdateStream() {
    this.updateInputsSubject.pipe(
      debounceTime(1400)  
    ).subscribe(({  }) => {
      this.fetchStatuses()
    });
  }

  checkIfHasVals(orderInfo:any) {

    if (!orderInfo)
      return false
    return Object.values(orderInfo).some(value => value !== "" && value !== null && value !== undefined && value !== false);
  }
  objectEntries(obj: any): { key: string, value: any }[] {
    return obj ? Object.entries(obj).map(([key, value]) => ({ key, value })) : [];
  }

  async fetchStatuses() {
    let statuses = await this.odooRpc.call('rb_delivery.order', 'get_statuses', [this.barcodesToFetch]);
    if (statuses && statuses.body && statuses.body.result && statuses.body.result.result) {
      let result = statuses.body.result.result;
      let isOrderInfo = Object.values(result).some(value => typeof value === 'object' && value !== null);
      if (isOrderInfo) {
        this.orderInfo = {};
        Object.entries(result).forEach(([barcode, data]) => {
          if (data && typeof data === 'object') {
            const statusKey = Object.keys(data).find(key => key.toLowerCase() === 'state_id');
            
            if (statusKey) {
              this.statuses[barcode] = (data as Record<string, any>)[statusKey];
              
              const { [statusKey]: _, ...orderDetails } : any = data;
              this.orderInfo[barcode] = orderDetails;
            } else {
              this.orderInfo[barcode] = data;
            }
          }
        });
      } else {
        Object.assign(this.statuses, result);
      }
  
      this.barcodesToFetch = [];
    }
  }

  close(){
    this.modalCtrl.dismiss()
  }

  removeBarCodeItem(barcode : string){
    this.barcodeList = this.barcodeList.filter(barcodeItem=>barcodeItem!=barcode)
  }

  toggleExpand(item:string){
    if(this.expandedItems.includes(item)){
      this.expandedItems=this.expandedItems.filter((i:any)=>i!=item)
    }
    else{
      this.expandedItems.push(item)
    }
  }

}
