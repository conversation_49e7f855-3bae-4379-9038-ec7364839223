<ion-header>
  <ion-toolbar mode="md" style="padding-right: 0.4rem;">
    <ion-buttons slot="start">
      <ion-icon size="large" src="../../../assets/icons/scan_update.svg"></ion-icon>
    </ion-buttons>
    <ion-title style="padding-inline-start: 10px;">{{ 'UPDATE_ORDERS_STATUS' | translate }}</ion-title>
    <ion-buttons slot="end">
      <ion-button color="dark" [disabled]="disabled" (click)="close()">
        <ion-icon size="large"  name="arrow-forward"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-list lines="none" style="background:#f8f8f8">
    <ion-card mode="ios" *ngFor="let item of barcodeList; let i = index" class="status-card">
      <ion-card-header style="padding: 0; width: 100%; place-self: center;">
        <ion-toolbar mode="md" style="--background:white;">
          <ion-title style="padding-inline: 7px;">
            <span class="sequence-text">
              {{ item }}
            </span>
            <ion-col size="3" class="status-chip" *ngIf="statuses.hasOwnProperty(item) && statuses[item]">
              {{ statuses[item] | translate }}
            </ion-col>
            <ion-col size="3" class="status-chip" style="background: var(--ion-color-danger);" *ngIf="statuses.hasOwnProperty(item) && !statuses[item]">
              <ion-icon color="white" class="icon" name="alert-circle" style="zoom: 1.5; inset-inline-start: -5px; top: 3.1px;"></ion-icon>
              {{ 'ORDER_NOT_FOUND' | translate }}
            </ion-col>
            <ion-col *ngIf="!statuses.hasOwnProperty(item) && !statuses[item]">
              <ion-spinner duration="3" style="zoom: .5;"/>
            </ion-col>
          </ion-title>
          <ion-buttons slot="end">
            <ion-button slot="end" size="1" class="icon remove-icon" (click)="removeBarCodeItem(item)">
              <ion-icon src="../../../assets/icons/remove.svg"></ion-icon>
            </ion-button>
          </ion-buttons>

        </ion-toolbar>
          
        
      </ion-card-header>
      <ion-card-content style="padding: 0; width: 95%; place-self: center; border-top: 1px solid #70707024;" *ngIf="messages[item]">
        <ion-toolbar mode="md" style="--background:white;" (click)="toggleExpand(item)">
          <ion-buttons slot="start">
            <ion-icon color="danger" class="icon" name="alert-circle" style="zoom: 1.7;"></ion-icon>
          </ion-buttons>
          <ion-title style="padding-inline: 7px; color: var(--ion-color-danger);">
            <span class="sequence-text" style="border: unset;">
              {{ 'PERMISSION_DENIED' | translate }}
            </span>
          </ion-title>
          <ion-buttons slot="end">
            <ion-button slot="end" size="1" class="icon remove-icon">
              <ion-icon *ngIf="!expandedItems.includes(item)" class="icon" name="chevron-forward"></ion-icon>
              <ion-icon *ngIf="expandedItems.includes(item)" class="icon" name="chevron-down"></ion-icon>
            </ion-button>
          </ion-buttons>

        </ion-toolbar>
        <ion-row  class="message-row">
          <ion-col *ngIf="expandedItems.includes(item)" size="12" class="message-content">
            {{ messages[item] | translate }}
          </ion-col>
        </ion-row>
      </ion-card-content>
      <ion-card-content style="padding: 0; width: 95%; place-self: center; border-top: 1px solid #70707024;" *ngIf="checkIfHasVals(orderInfo[item])">
        <ion-toolbar mode="md" style="--background:white;" (click)="toggleExpand(item)">
          <ion-buttons slot="start">
            <ion-img style=" width: 25px;padding-inline-end: 4px;" src="../../../assets/icon/order_info.svg"> </ion-img>
          </ion-buttons>
          <ion-title style="padding-inline: 7px; color: #333333">
            <span class="sequence-text" style="border: unset;">
              {{ 'ORDER_INFO' | translate }}
            </span>
          </ion-title>
          <ion-buttons slot="end">
            <ion-button slot="end" size="1" class="icon order-info-icon">
              <ion-icon *ngIf="!expandedItems.includes(item)" class="icon" name="chevron-forward"></ion-icon>
              <ion-icon *ngIf="expandedItems.includes(item)" class="icon" name="chevron-down"></ion-icon>
            </ion-button>
          </ion-buttons>

        </ion-toolbar>
        <ion-row  class="message-row">
          <ion-col *ngIf="expandedItems.includes(item)" size="12" class="order-content">
            <ng-container *ngFor="let entry of objectEntries(orderInfo[item])">
              <div class="row" *ngIf="entry.value">
                <span class="key">{{ entry.key }}:</span>
                <span class="value">{{ entry.value }}</span>
              </div>
            </ng-container>
          </ion-col>          
        </ion-row>
      </ion-card-content>
    </ion-card>
  </ion-list>
</ion-content>

<ion-footer class="ion-no-border">
  <ion-toolbar>
    <ion-row class="footer-row">
      <ion-col>
        <ion-button style="--background:linear-gradient(243deg, #7FFAB6 15%, #2BC871 100%);" mode="ios" expand="block" [disabled]="disabled" (click)="updateState()">
          {{ 'CONTINUE' | translate }}
        </ion-button>
      </ion-col>
      <ion-col>
        <ion-button style="--background:linear-gradient(243deg, #EE8413 15%, #AE5A00 100%);" mode="ios" expand="block" [disabled]="disabled" (click)="scanAndUpdateBarcodeList()">
          {{ 'MORE' | translate }}
        </ion-button>
      </ion-col>
    </ion-row>
  </ion-toolbar>
</ion-footer>