import { Component, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { ErrorState } from './ngrx-store/error/store';
import * as errorSelectors from './ngrx-store/error/store/selectors';
import * as errorActions from './ngrx-store/error/store/actions';
import { AlertController, ToastController, ModalController, Platform } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { ClientConfigurationState } from './ngrx-store/client-configuration/store/state';
import * as clientConfigurationsActions from './ngrx-store/client-configuration/store/actions';
import * as clientConfigurationsSelector from './ngrx-store/client-configuration/store/selectors';
import { GeneralConfigurationState } from './ngrx-store/general-configuration/store/state';
import * as generalConfigurationsActions from './ngrx-store/general-configuration/store/actions';
import { NetworkProvider } from './services/network-services';
import { Router } from '@angular/router';
import { filter, take } from 'rxjs';
import { OdooJsonRPC } from './services/odooJsonRPC';
import { AndroidPermissions } from '@awesome-cordova-plugins/android-permissions/ngx';
import { Device } from '@capacitor/device';
import { NativeSettings, AndroidSettings } from 'capacitor-native-settings';
import { dialogService } from './services/error-handlar-service';
import { ErrorHandlerService } from './services/error-handler-service';
import { Storage } from '@ionic/storage-angular';
import { OpenTimerService } from './services/openTimerServices';
import { StatusState } from './ngrx-store/status/store/state';
import { Ondemandservice } from './services/ondemand-service';

import { animate, state, style, transition, trigger } from '@angular/animations';
import { SplashScreen } from '@capacitor/splash-screen';
import { environment } from 'src/environments/environment';
import { Network } from '@capacitor/network';
import OneSignal from 'onesignal-cordova-plugin';


@Component({
  selector: 'app-root',
  templateUrl: 'app.component.html',
  styleUrls: ['app.component.scss'],
  animations: [
    trigger('fadeOut', [
      state('visible', style({
        opacity: 1
      })),
      state('hidden', style({
        opacity: 0
      })),
      transition('visible => hidden', [
        animate('0.5s')
      ])
    ])
  ]

})
export class AppComponent implements OnInit {
  showSplashScreen: boolean = true
  splashScreenState = 'visible';
  showConnectionStatus: boolean = false;
  isRefreshingConnection: boolean = false;
  constructor(
    private errorStore: Store<ErrorState>,
    private toastCtrl: ToastController,
    private translate: TranslateService,
    private clientConfigurationStore: Store<ClientConfigurationState>,
    private generalConfigurationStore: Store<GeneralConfigurationState>,
    private networkProvider : NetworkProvider,
    private router:Router,
    private odooJsonRPC:OdooJsonRPC,
    private androidPermissions: AndroidPermissions,
    private alertCtrl: AlertController,
    private modalCtrl: ModalController,
    private dialogService: dialogService,
    private platform: Platform,
    private errorService:ErrorHandlerService,
    private storage : Storage,
    private openTimerServices : OpenTimerService,
    private ondemandService : Ondemandservice
  ) {
    this.platform.ready().then(() => {
      SplashScreen.hide();
      document.addEventListener("resume", onResume, false);
      let self=this
      async function onResume() {
        self.checkNotifications()
      }
      self.checkNotifications()
    })
    this.initializeErrorListener()
    this.initializeNetworkListener()
    this.detectXiaomiDevice()
  }
  async checkNotifications() {
    let companyInfo = await this.storage.get('companyInfo')
    if((this.ondemandService && this.ondemandService.onDemandServer) || (companyInfo && companyInfo.is_ondemand) ){
      this.checkActiveTimerOrders()
    }
    this.checkLocalNotification()
  }
  async detectXiaomiDevice() {
    // this permission is specifec for xiaomi to be able to redirect to app when click on the notification
    let deviceInfo = await Device.getInfo()
    if(deviceInfo.platform.toLowerCase() == 'android' && deviceInfo.manufacturer.toLowerCase() == "xiaomi" && Number(deviceInfo.osVersion) <= 11){
      this.checkAndRequestPermissionDisplayOverOtherApps()
    }
    
  }

  async checkAndRequestPermissionDisplayOverOtherApps(background=true) {
    // this permission is specifec for xiaomi to be able to redirect to app when click on the notification
    this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.SYSTEM_ALERT_WINDOW).then(
      async result => {
        if(!result.hasPermission){
          let permissionAlert = await this.alertCtrl.create({
            header:this.translate.instant("PLEASE_ALLOW_DISPLAY_POPUP_WINDOWS")+" "+(background?this.translate.instant('WHILE_RUNNING_IN_BACKGROUND'):"") +"\n" +
            this.translate.instant("THIS_PERMISSION_IS_REQUIRED_FOR_THE_APP_TO_FUNCTION_PROPERLY"),
            message:this.translate.instant("TO_DO_SO_OPEN_SETTINGS_THEN") + "\n" + 
            this.translate.instant("OTHER_PERMISSIONS") + " => " + this.translate.instant("DISPLAY_POPUP_WINDOWS") +(background?this.translate.instant('WHILE_RUNNING_IN_BACKGROUND'):""),
            mode:"ios",
            buttons:[{
              text:this.translate.instant("OPEN_SETTINGS"),
              handler:()=>{
                permissionAlert.dismiss()
                if(background)
                  NativeSettings.openAndroid({
                    option:AndroidSettings.ApplicationDetails
                  }).then(()=>{
                    this.checkAndRequestPermissionDisplayOverOtherApps(false)
                  })
                else
                  this.androidPermissions.requestPermissions([this.androidPermissions.PERMISSION.SYSTEM_ALERT_WINDOW])
              },
              htmlAttributes:{'color':'var(--ion-color-success)'},
              role:"destructive"
            },{
              text:this.translate.instant("CANCEL"),
              role:"destructive"
            }]
          })
          permissionAlert.present()
        }
        
      },
      err => this.androidPermissions.requestPermission(this.androidPermissions.PERMISSION.SYSTEM_ALERT_WINDOW)
    );
    
    
  }
  ngOnInit(): void {
    this.getColorConfiguration();
    this.clientConfigurationStore.dispatch(new clientConfigurationsActions.LoadHttp());
    this.generalConfigurationStore.dispatch(new generalConfigurationsActions.LoadHttp());
    this.storage.get('lang').then(lang=>{
      this.translate.use(lang.language);
      document.documentElement.dir = lang.direction;
    })
    
    this.initializeAppSplashScreen();
  }

 async initializeAppSplashScreen() {
  let showSplash = false
    this.clientConfigurationStore.select(clientConfigurationsSelector.selectClientConfigurationByKeys(['mobile_new_splash_screen'])).pipe(
      filter((keys) => keys && keys.length > 0), 
      take(1)
    ).subscribe(configurationKeys => {
      showSplash = true
      if (configurationKeys && configurationKeys.length > 0) {
        for (let configurationKey of configurationKeys) {
          if (configurationKey.value) {
            setTimeout(() => {
              this.splashScreenState = 'hidden';
              setTimeout(() => {
                this.showSplashScreen = false;
              }, 500);
            }, 4000);
          } else {
            this.showSplashScreen = false;
          }
        }
      } else {
        this.showSplashScreen = false;
        this.splashScreenState = 'hidden';
      }
    });
    setTimeout(() => {
      if (!showSplash) {
        this.showSplashScreen = false;
      }
    }, 1500);
  }

  getColorConfiguration() {
    let setIconColor = false
    this.getFormsBackground()
    this.clientConfigurationStore.select(clientConfigurationsSelector.selectClientConfigurationByKeys(['primary_color_in_mobile','secondary_color_in_mobile', 'mobile_card_icon_colors', 'mobile_main_icons_color'])).pipe(filter((keys) => keys && keys.length > 0), take(1)).subscribe(configurationKeys => {
      if (configurationKeys && configurationKeys.length > 0) {
        for (let configurationKey of configurationKeys) {
          if (configurationKey.key == 'primary_color_in_mobile') {
            document.documentElement.style.setProperty('--ion-color-primary', configurationKey.text)
          }
          else if (configurationKey.key == 'secondary_color_in_mobile') {
            document.documentElement.style.setProperty('--ion-color-secondary', configurationKey.text)
          }else if (configurationKey.key == 'mobile_card_icon_colors') {
            setIconColor = true
            document.documentElement.style.setProperty('--ion-card-icon-colors', configurationKey.text)
          }else if (configurationKey.key == 'mobile_main_icons_color' && configurationKey.text) {
            document.documentElement.style.setProperty('--ion-main-icons-primary', configurationKey.text)
          }
        }

        if (document.documentElement.style.getPropertyValue('--ion-main-icons-primary') === '') {
          document.documentElement.style.setProperty('--ion-main-icons-primary', document.documentElement.style.getPropertyValue('--ion-color-primary'));
        }

        if (!setIconColor) {
          document.documentElement.style.setProperty('--ion-card-icon-colors', '#b88b11')
        }
        
      }
    })
  }
  getFormsBackground() {
    if (environment.db != 'master') {
      setTimeout(()=>{
        this.odooJsonRPC.searchRead('res.company',[['form_background','!=',false]],['id'],1,0,'').then(response=>{
          if(response && response.body && response.body.result && response.body.result.result && response.body.result.result.length>0){
            document.documentElement.style.setProperty('--forms-background', 'url('+environment.url+'/web/image?model=res.company&id=1&field=form_background)')
          }
        })
      },1500)
    }
  }
  async initializeNetworkListener() {
    this.networkProvider.initializeNetworkEvents();
    let isNetworkOffline = false;
    
    if(!(await Network.getStatus()).connected){
      this.showConnectionStatus = true;
    }
    
    this.networkProvider.getObservable().subscribe(async networkStatus => {
      if (networkStatus && networkStatus.connected === false && !isNetworkOffline) {
        this.showConnectionStatus = true;
        isNetworkOffline = true;
      } else if (networkStatus && networkStatus.connected === true && isNetworkOffline) {
        this.showConnectionStatus = false;
        isNetworkOffline = false;
      }
    });
  }

  async onConnectionRefresh() {
    this.isRefreshingConnection = true;
    
    const networkStatus = await Network.getStatus();
    
    if (networkStatus.connected) {
      this.showConnectionStatus = false;
    }
    
    this.isRefreshingConnection = false;
  }
  
  initializeErrorListener() {
    this.errorStore
      .select(errorSelectors.selectAllData)
      .subscribe(async (error: any) => {
        if (error && error.length > 0) {
          error = error[error.length - 1]
          let isCorsError= error.message && error.message.includes('Unknown Error') && (await Network.getStatus()).connected && this.platform.is('ios');
          let isConcurrentUpdate = error.message && (error.message.toLowerCase().includes('could not serialize access due to concurrent update'.toLowerCase()) || error.message.toLowerCase().includes('transaction block'.toLowerCase()))
          if(error.message == 'Token invalid or expired'){
            this.odooJsonRPC.clearSession()
          }
          else if(isCorsError){
            
            sessionStorage.removeItem('playerId');
            Promise.all([
              this.storage.remove('ondemandServer'),
              this.storage.remove('companyInfo'),
            ])
          }
          var currType = error.errorType
          currType = currType === '' ? 'warning' : currType
          
          this.dialogService.error({             
            input: isCorsError?this.translate.instant('IOS_NOT_SUPPORTED'): isConcurrentUpdate?this.translate.instant('ERROR_WHILE_SENDING_REQUEST'): this.translate.instant(error.message),
            message: error.message,
            whatToDo:isCorsError?this.translate.instant('PLEASE_CONTACT_SUPPORT_TO_ACTIVATE_IOS_SUPPORT'): isConcurrentUpdate?this.translate.instant('PLEASE_WAIT_A_MOMENT_AND_TRY_AGAIN'): this.translate.instant(error.message),
            code: error.code, 
            type: currType,
            title: error.title,
            reloadOnDismiss:isCorsError
          });
          this.errorStore.dispatch(new errorActions.ClearUserTypeError())
        }
      });
  }

  checkActiveTimerOrders(){
    this.storage.get('user_info').then(userInfo =>{
      if(userInfo && Object.keys(userInfo).length > 0 && userInfo[0].id){
        this.openTimerServices.openAlertTimer([['active_notification_data', '!=', false],['state','in',['pending_nearest_driver','manual_assigned_driver']],['assign_to_agent','=',userInfo[0].id]])
      }
      })
  }

  checkLocalNotification(){
    OneSignal.Notifications.clearAll()
}

}

