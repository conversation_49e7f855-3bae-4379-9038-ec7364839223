// error-handler.service.ts
import { Injectable } from '@angular/core';
import { ToastController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';

@Injectable({
  providedIn: 'root'
})
export class ErrorHandlerService {

  constructor(private toastController: ToastController,private translate: TranslateService) {}

  public presentError(headerMessage:string ,errorMessage: string) {
    this.toastController.create({
      header:this.translate.instant('CONNECTION_ERROR'),
      message: this.translate.instant('PLEASE_CHECK_YOUR_INTERNET_CONNECTION_CLICK_ON_RELOAD_BUTTON_TO_REFRESH'),
      position: 'bottom',
      buttons: [
        {
          text: '🔄️',
          handler: () => {
            console.log('Reloading the page...');
            window.location.reload();
          }
        },
        {
          text: '❌',
          role: 'cancel',
          handler: () => {
            console.log('Toast dismissed.');
          }
        }
      ]
    }).then(toast => {
        toast.present().catch(err => {
            console.error('Error presenting toast', err);
        });
    }).catch(err => {
        console.error('Error creating toast', err);
    });
    
  }
}
