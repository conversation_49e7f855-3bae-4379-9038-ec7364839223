import { Injectable } from "@angular/core";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Toast<PERSON><PERSON>roll<PERSON>, Modal<PERSON>ontroller } from '@ionic/angular';
import { ErrorDialogComponent } from "../components/error-dialog/error-dialog.component";
import { keys } from "lodash";
import { debounceTime, Subject } from "rxjs";

/*

USAGE:

JSON STRUCTURE:

{
    "input": "" // use if response is comming from the web
    "message": "Error message", default: 'An error has occurded'
    "WHAT_TO_DO": "What to do", default: 'Please contact adminstrator'
    "code": "Error code", default: -1
}  // non of them are requierd 


dialogService.error(YOUR JSON OBJECT)
dialogService.warning(YOUR JSON OBJECT)
dialogService.info(YOUR JSON OBJECT)
dialogService.success(YOUR JSON OBJECT)


*/

interface INPUT {
    [key: string]: any; 
    type?: string; 
  }

  @Injectable({
    providedIn: 'root'
  })
export class dialogService {
    openModalSubscription!:Subject<HTMLIonModalElement>
    constructor(
        private modalCtrl: ModalController
    ) { 
        this.openModalSubscription = new Subject<HTMLIonModalElement>()
        this.openModalSubscription.pipe(debounceTime(500)).subscribe(modal=>{
            modal.present()
        })
    }


    
    public error(input: INPUT | void) {

        if(input) {
            if (input['type'] == undefined) {
                input['type'] = 'error';
            }
        }
        
        this.modalCtrl.getTop().then(openedModal=>{
            if(input && keys(input).includes('message') && input['message'] == 'Token invalid or expired'){
                openedModal?.dismiss()
            }
            this.modalCtrl.create({
                component: ErrorDialogComponent,
                cssClass: "errors-dialog",
                componentProps: input ? input : { type: 'error' }
            }).then(modal => {
                this.openModalSubscription.next(modal)
                if((input as any).reloadOnDismiss){
                    modal.onDidDismiss().then(()=>{
                        window.location.reload()
                    })
                }
            })
        })
        
    }

    public warning(input: INPUT | void) {

        if(input) {
            input['type'] = 'warning';
        }

        this.modalCtrl.create({
            component: ErrorDialogComponent,
            cssClass: "errors-dialog",
            componentProps: input ? input : { type: 'warning' }
        }).then(modal => {
            this.openModalSubscription.next(modal)
        })
    }

    public info(input: INPUT | void) {

        if(input) {
            input['type'] = 'info';
        }

        this.modalCtrl.create({
            component: ErrorDialogComponent,
            cssClass: "errors-dialog",
            componentProps: input ? input : { type: 'info' }
        }).then(modal => {
            this.openModalSubscription.next(modal)
        })

    }

    public success(input: INPUT | void) {

        if(input) {
            input['type'] = 'success';
        }

        this.modalCtrl.create({
            component: ErrorDialogComponent,
            cssClass: "errors-dialog",
            componentProps: input ? input : { type: 'success' }
        }).then(modal => {
            this.openModalSubscription.next(modal)
        })

    }
}