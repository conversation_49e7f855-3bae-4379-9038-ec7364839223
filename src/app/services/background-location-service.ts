import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import BackgroundGeolocation, { AuthorizationStatus, HttpEvent, Location, Config , State } from "@transistorsoft/capacitor-background-geolocation";
import config from 'capacitor.config';
import { environment } from 'src/environments/environment';
import { Storage } from '@ionic/storage';
import { AlertController, ToastController } from '@ionic/angular';
import { Capacitor } from '@capacitor/core';
import { BatteryOptimization } from '@capawesome-team/capacitor-android-battery-optimization';

import { AndroidSettings, IOSSettings, NativeSettings } from 'capacitor-native-settings';




import { Observable, Subject } from 'rxjs';

@Injectable()
export class BackgroundLocationService {
  userId: any;

  locationChange$: Subject<Location> = new Subject<Location>()
  
  constructor(
    private translate:TranslateService,
    private storage:Storage,
    private toastCrtl : ToastController,
    private alertCtrl : <PERSON>ert<PERSON><PERSON>roller,
  ){

  }

  async stop() {
    const isStarted = (await BackgroundGeolocation.getState()).enabled;
    if(isStarted){
      BackgroundGeolocation.stop()
    }  
  }

  async requestBackgroundPermission(){
      BackgroundGeolocation.requestPermission();
      
        
  }





  async setupBackgroundLocation(token: string, forcePrompt = false) {
    try {
      const isPermissionEnabled = await this.checkBackgroundLocationPermission();
      const neverAskAgain = await this.storage.get('neverRequestBackgroundLocation');
      
      // User needs permission prompt or "forcePrompt" is true
      if (!isPermissionEnabled && (!neverAskAgain || forcePrompt)) {
        const alertResponse = await this.showLocationPrompt(forcePrompt);
        const { accepted, neverAskAgain: userNeverAskAgain } = alertResponse;
        
        await this.storage.set('neverRequestBackgroundLocation', userNeverAskAgain);
        if (!accepted) return;  // Exit if user didn't accept
      } else if (isPermissionEnabled && forcePrompt) {
        this.showToast(this.translate.instant('PERMISSION_ALREADY_GRANTED'));
        return;
      } else if (neverAskAgain) {
        return;  // User has opted to never ask again
      }
  
      // Initialize background geolocation tracking
      await this.initBackgroundGeolocation(token);
    } catch (error) {
      console.error('Error in setupBackgroundLocation:', error);
    }
  }
  
  // Helper function to check background location permission
  private async checkBackgroundLocationPermission(): Promise<boolean> {
    const providerState = await BackgroundGeolocation.getProviderState();
    return providerState?.status === BackgroundGeolocation.AUTHORIZATION_STATUS_ALWAYS;
  }
  
  // Helper function to show permission prompt
  private async showLocationPrompt(forcePrompt: boolean) {
    const buttons = [
      {
        text: this.translate.instant('ACCEPT'),
        handler: () => ({ accepted: true, neverAskAgain: false }),
        role: 'success',
      },
      {
        text: this.translate.instant('REJECT'),
        handler: () => ({ accepted: false, neverAskAgain: false }),
        role: 'cancel',
      },
    ];
    
    if (!forcePrompt) {
      buttons.push({
        text: this.translate.instant('NEVER_ASK_AGAIN'),
        handler: () => ({ accepted: false, neverAskAgain: true }),
        role: 'destructive',
      });
    }
  
    const alert = await this.alertCtrl.create({
      header: this.translate.instant('BACKGROUND_PROMPT_HEADER'),
      message: this.translate.instant('BACKGROUND_PROMPT_MESSAGE'),
      mode: 'ios',
      buttons,
    });
    await alert.present();
    return (await alert.onDidDismiss()).data;
  }
  
  // Helper function to show a toast notification
  private async showToast(message: string) {
    const toast = await this.toastCrtl.create({
      header: message,
      duration: 3000,
      position: 'top',
      mode: 'ios',
    });
    await toast.present();
  }
  
  // Initialize Background Geolocation
  private async initBackgroundGeolocation(token: string) {
    this.userId = (await this.storage.get('user_info') as any)?.[0]?.id;
  
    // Define headers
    const headers = {
      'Content-Type': 'application/json; charset=utf-8',
      'Authorization': `Bearer ${token}`,
    }
    const geoConfig:Config = {
        desiredAccuracy: BackgroundGeolocation.DESIRED_ACCURACY_HIGH,
        logLevel: BackgroundGeolocation.LOG_LEVEL_OFF,
        reset: false,
        debug: false,
        backgroundPermissionRationale: {
          title: this.translate.instant("ALLOW ") + config.appName + this.translate.instant(" TO_ACCESS_THIS_DEVICE'S_LOCATION_IN_THE_BACKGROUND?"),
          message: this.translate.instant("AS_A_LOGISTICS_COMPANY")+" " + config.appName +" "+ this.translate.instant("NEEDS_TO_COORDINATE_MULTIPLE_DRIVERS_AND_NAVIGATE_TO_VARIOUS_LOCATIONS_FOR_ORDER_DELIVERY._CONTINUOUS_BACKGROUND_LOCATION_ACCESS_IS_ESSENTIAL_FOR_PROVIDING_ACCURATE_AND_TIMELY_UPDATES,_EFFICIENT_ROUTING,_AND_THE_PRIORITIZATION_OF_ORDER_DELIVERY_BASED_ON_LOCATION._THIS_DATA_IS_USED_EXCLUSIVELY_FOR_OPERATIONAL_LOGISTICS_AND_IS_NOT_SHARED_WITH_THIRD_PARTIES."),
          positiveAction: this.translate.instant("AGREE_AND_CONTINUE"),
          negativeAction: this.translate.instant("CANCEL")
        },
        disableMotionActivityUpdates: true,
        disableLocationAuthorizationAlert:true,
        enableHeadless: true,
        httpRootProperty: "params",
        stopOnTerminate: false, // Keep tracking even if the app is terminated
        startOnBoot: true, // Start tracking on device boot
        locationAuthorizationRequest: 'Always',
        triggerActivities: "in_vehicle, on_bicycle, on_foot, running, walking",
        disableStopDetection: true,
        isMoving: true,
        allowIdenticalLocations: true,
        enableTimestampMeta: true,
        stopOnStationary: false,
        distanceFilter: 50,
        extras: { 'user_id': this.userId ,'token':token},
        headers:headers,
        method:'POST',
        url:environment.url +'/api/update_location_method',
        
        
    }
  
    await BackgroundGeolocation.setConfig(geoConfig);
    
    
    // Handle permission request
    const permissionState:AuthorizationStatus = await BackgroundGeolocation.requestPermission();
    if (permissionState !== 3) {
      // Permission not granted
      return
    }else{
      // Permission is granted
      BackgroundGeolocation.ready(geoConfig)

      this.setupGeolocationEventListeners();
      this.checkAndRequestBatteryOptimization()
    }
  }
  
  // Setup Geolocation event listeners
  private setupGeolocationEventListeners() {
    BackgroundGeolocation.onLocation(async (location) => {
      const { latitude, longitude } = location.coords;
      this.locationChange$.next(location)
      console.log('Location Update:', latitude, longitude);
    });
  
    BackgroundGeolocation.onMotionChange(event => {
      console.log('Motion change:', event.isMoving, event.location);
    });
  
    BackgroundGeolocation.onActivityChange(event => {
      console.log('Activity change:', event);
    });
  
    BackgroundGeolocation.onHttp(event => {
      if (!event.success) {
        this.logFailedHttpRequest(event);
      }
    });
  
    BackgroundGeolocation.onProviderChange(async event => {
      if (event.enabled) {
        this.startTracking()
        let openedAlert = await this.alertCtrl.getTop()
        if(openedAlert){
          openedAlert.dismiss()
        }
      }
      else{
        this.openSettings()
      }
    });

  }

  openSettings() {
    this.alertCtrl.create({
      header: this.translate.instant('LOCATION_SERVICES_DISABLED'),
      message: this.translate.instant('PRESS_OPEN_SETTING_AND_ENABLE_LOCATION_SERVICES'),
      mode: 'ios',
      buttons: [{
        text: this.translate.instant('NO'),
        role: 'destructive',
        handler: () => { }
      }, {
        text: this.translate.instant('OPEN_SETTING'),
        handler: () => {
          return NativeSettings.open({
            optionAndroid: AndroidSettings.Location,
            optionIOS: IOSSettings.LocationServices
          });
        }
      }]
    }).then(alert => alert.present());
  }
  
  async startTracking() {
    const isStarted = (await BackgroundGeolocation.getState()).enabled;
    if(!isStarted){
      BackgroundGeolocation.start()
    }
  }

  
  // Handle failed HTTP request logs
  private async logFailedHttpRequest(event:HttpEvent) {
    const logs = (await this.storage.get('geo_http_logs')) || [];
    logs.push({ ...event, createDate: new Date() });
    this.storage.set('geo_http_logs', logs.slice(-5)); // Keep last 5 logs
  }


  async checkAndRequestBatteryOptimization() {
    try {
      // Check if the battery optimizations is enabled
      const batteryOptimizationEnabled = await this.isBatteryOptimizationEnabled();

      // Request to ignore battery optimization if it's enabled
      if (batteryOptimizationEnabled) {
          this.showBatteryOptimizationAlert();
          return;
      }
      else{
        this.startTracking()
      }


      
    } catch (error) {
      console.error('Error checking battery optimization status:', error);
    }
  }

  
  private async showBatteryOptimizationAlert() {
    
    const alert = await this.alertCtrl.create({
      header: this.translate.instant('BATTERY_OPTIMIZATION_PROMPT_HEADER'),
      message: this.translate.instant('BATTERY_OPTIMIZATION_PROMPT_MESSAGE'),
      mode: 'ios',
      buttons: [
        {
          text: this.translate.instant('OK'),
          handler: () => {
            this.requestIgnoreBatteryOptimization()
          },
        },
        {
          text: this.translate.instant('CANCEL'),
          role: 'cancel',
        },
      ],
    });
    await alert.present();
  }

  isBatteryOptimizationEnabled = async () => {
    if (Capacitor.getPlatform() !== 'android') {
      return false;
    }
    const { enabled } = await BatteryOptimization.isBatteryOptimizationEnabled();
    return enabled;
  };
  
  
  requestIgnoreBatteryOptimization = async () => {
    if (Capacitor.getPlatform() !== 'android') {
      return;
    }
    await BatteryOptimization.requestIgnoreBatteryOptimization();
  };
}




