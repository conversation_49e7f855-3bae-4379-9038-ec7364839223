// http://ionicframework.com/docs/theming/
@import '~@ionic/angular/css/core.css';
@import '~@ionic/angular/css/normalize.css';
@import '~@ionic/angular/css/structure.css';
@import '~@ionic/angular/css/typography.css';
@import '~@ionic/angular/css/display.css';
@import '~@ionic/angular/css/padding.css';
@import '~@ionic/angular/css/float-elements.css';
@import '~@ionic/angular/css/text-alignment.css';
@import '~@ionic/angular/css/text-transformation.css';
@import '~@ionic/angular/css/flex-utils.css';
@import '@angular/material/prebuilt-themes/indigo-pink.css';


ion-app.cameraView ion-content{
    --background: transparent none !important;
  }
  ion-label{
    -webkit-tap-highlight-color: rgba(0,0,0,0);
    -webkit-touch-callout: text;
    -webkit-user-select: text;
    -khtml-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
    --ion-default-font: 'cairo'
  }
  @font-face {
    font-family: 'Cairo';
    font-style: normal;
    font-weight: 500;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/cairo/v17/SLXVc1nY6HkvangtZmpcWmhzfH5la2gcQyyS4J0.woff2) format('woff2');
    unicode-range: U+0600-06FF, U+200C-200E, U+2010-2011, U+204F, U+2E41, U+FB50-FDFF, U+FE80-FEFC;
  }
  /* latin-ext */
  @font-face {
    font-family: 'Cairo';
    font-style: normal;
    font-weight: 500;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/cairo/v17/SLXVc1nY6HkvangtZmpcWmhzfH5la2gcSCyS4J0.woff2) format('woff2');
    unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
  }
  /* latin */
  @font-face {
    font-family: 'Cairo';
    font-style: normal;
    font-weight: 500;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/cairo/v17/SLXVc1nY6HkvangtZmpcWmhzfH5la2gcRiyS.woff2) format('woff2');
    unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
  }

  ion-popover [popover]:not(:popover-open):not(dialog[open]) {
    display: contents;
}
app-form-creator{
  width: 100%;
}
app-form-input{
  display: block;
  height: fit-content;
}
ion-content{
  --ion-default-font: 'cairo'
}

.custom-content{
  background: var(--content-background);
  --background: transparent;
  background-repeat: no-repeat;
  background-size: cover;
}

.forms-background{
  background: var(--forms-background);
  --background: transparent;
  background-repeat: no-repeat;
  background-size: cover;
}

.input-wrapper .label-text-wrapper{
  margin-top: -3px !important;
  height: 18px !important;
}

.windowed-order{
  --width: 95% !important;
  --height: 100% !important;
  --background: #00000000;
}

.windowed-modal {
  --width: 70% !important;
  --height: 400px !important;
  --border-radius:15px;
  background: #00000050;

}

.windowed-modal-sort-and-destribute {
  --border-radius:15px;
  background: #00000050;

}

.request-collection-modal {
  --height: 340px !important;
  --width: 85vw !important;
  --border-radius:15px;
  background: #00000050;

}
@media (max-width: 768px) {
  .request-collection-modal {
    --width: 95vw;
    padding: 20px;
    --border-radius:15px;
    background: #00000050;

  }
}


.request-agent-collection-modal {
  --height: 400px !important;
  --width: 85vw !important;
  --border-radius:15px;
  background: #00000050;

}
@media (max-width: 768px) {
  .request-agent-collection-modal {
    --width: 95vw;
    padding: 20px;
    --border-radius:15px;
    background: #00000050;

  }
}
  


.success-request-collection-modal{
  --height: fit-content !important;
  --border-radius:15px;
  background: #00000050;
  --width:85%;
  zoom: 0.8;
}

.timer-alert-style{
  --width: 90vw;
  --height: 90vh;
  background: linear-gradient(154deg, rgba(73,73,73,0.1) 58%, rgba(212,204,204,0.2) 0%, rgba(4,0,2,0.6) 108%);
}

.manual-assign-timer-alert-style{
  --width: 90vw;
  --height: 55vh;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5); /* Adjust the transparency as needed */
  z-index: 999; /* Adjust the z-index to make sure it's above other content */
}

.windowed-medium-modal{
  --width: 70% !important;
  --height: 28% !important;
  --border-radius:15px;
  background: #00000050;
}


.showattchment{
  --width: 80% !important;
  --height: 50% !important;
  --border-radius:15px;
}

.centered-modal {
  --width: 90vw;
  --height: 70vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.8);
}

ion-img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.fit-window{
  --height: fit-content !important
}

.follow-orders-list-dialog {
  --border-radius:15px;
}

.exclude-hide{
  visibility: visible !important;
}

.next{
  animation: slideOut 0.5s ease-in-out;
}

.previous{
  animation: slideIn 0.5s ease-in-out;
}
.custom-fab {
  margin-right: 20px;
  margin-bottom: 20px;
  --background-activated: rgb(54, 53, 53);
  --background: rgb(54, 53, 53);
  --box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}
.custom-fab.activated {
  background: rgb(54, 53, 53) !important;
}
.custom-fab .custom-fab-icon {
  font-size: 40px;
  --ionicon-stroke-width: 50px;
}


@keyframes slideIn {

  from {
    transform: translateX(-400%);
  }
  to {
    transform: translateX(0);
  }
}


body.scanner-active {
  --background: transparent;
  --ion-background-color: transparent;
}

.hidden-app{
  opacity: 0;
}

.action-sheet-container.sc-ion-action-sheet-ios {
  max-height: 70vh !important;
}

@keyframes slideOut {

  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(-400%);
  }
}

.alert-message {
  align-items: center;
  display: flex;
  justify-content: center;
  text-align: center;
}

ion-modal{
  visibility: var(--modal-visibility) !important;
  --background: #f7f7f7;
}

ion-tab-bar{
  visibility: var(--modal-visibility) !important;
}

.terms-and-conditions {
  --max-width: 95vw;
  --max-height: 80vh;
  .alert-message {
    display: unset;
  }
}

.request-collection-button{
  background: #F9AC00 0% 0% no-repeat padding-box !important;
  border-radius: 10px !important;
  color: aliceblue !important;
}

.action-sheet-button-inner.sc-ion-action-sheet-ios {
  display: flex !important;
  position: relative !important;
  -ms-flex-flow: row nowrap !important;
  flex-flow: row nowrap !important;
  -ms-flex-negative: 0 !important;
  flex-shrink: 0 !important;
  -ms-flex-align: center !important;
  align-items: center !important;
  -ms-flex-pack: center !important;
  justify-content: flex-start !important;
  pointer-events: none !important;
  width: 100% !important;
  height: 100% !important;
  z-index: 1 !important;
}

ion-modal.errors-dialog {
  --width: 80%; 
  --height: auto !important;
  --dialog-height: auto !important;
  --border-radius: 15px; 
  --background: rgb(255, 255, 255);
  --content-background: rgb(255, 255, 255);
  --ion-toolbar-background: rgb(255, 255, 255);
  --overflow: hidden !important;
  overflow-y: hidden !important;
  position: absolute;
}

.announcement-dialog {
 --background:none;
}

.error-dialog.scroll-y {
  overflow-y: unset !important;
}

.modal-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16px;
}

ion-modal.pdf-dialog {
  --width: 92%;
  --height: 200px;
  --border-radius: 20px; 
  position: absolute;
}

.pdf-dialog.scroll-y {

  overflow-y: unset !important;

}

.modal-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16px;
}

.button-style{
  width: -webkit-fill-available;
  margin: 0;
  --border-radius:var(--button-radius);
  border-radius:var(--button-radius);
  --box-shadow: var(--button-shadow);
  font-size: 10pt;
  ion-label{
    color: var(--ion-color-white);
  }
}

.customized-input-1 .input-wrapper.sc-ion-input-md {
  font-family: "cairo";
  font-size: 15px;
  box-shadow: rgba(0, 0, 0, 0.4) 0px 30px 90px;
  --border-color: transparent;
}

.sort-and-destribute {
  --backdrop-opacity: 0.6 !important;
}

ion-modal.sort-and-destribute-dialog {
  --width: 92%;
  --height: 100% !important;
  --border-radius: 20px; 
  position: absolute;
  --backdrop-opacity: 0.6 !important;

  --width: 80%; 
  --height: auto !important;
  --dialog-height: auto !important;
  --border-radius: 15px; 
  --background: rgb(255, 255, 255);
  --content-background: rgb(255, 255, 255);
  --ion-toolbar-background: rgb(255, 255, 255);
  --overflow: hidden !important;
  overflow-y: hidden !important;
  position: absolute;
}

.gm-style-iw-c {
  max-width: 310px !important;
  width: 310px !important;
}

.sliding-modal {
  position: fixed;
  bottom: -200vh;
  left: 0;
  width: 100vw;
  max-height: 160vh;
  background: transparent;
  border-radius: 15px 15px 0 0;
  transition: transform 1s ease-in-out;
  overflow-y: auto;
}

.sliding-modal.slide-out {
  transition: transform 2s ease-in-out;
  transform: translateY(200vh);
}


.sliding-modal app-record-item {
  max-height: 40vh;
  overflow-y: auto;
  display: block;
  width: 100%;
}

ion-modal.otp-verification-modal {
  --width: 90%;
  --height: auto !important;
  --dialog-height: auto !important;
  --border-radius: 15px; 
  --background: rgb(255, 255, 255);
  --content-background: rgb(255, 255, 255);
  --ion-toolbar-background: rgb(255, 255, 255);
  --overflow: hidden !important;
  overflow-y: hidden !important;
  position: absolute;
}