// DO NOT EDIT THIS FILE! IT IS GENERATED EACH TIME "capacitor update" IS RUN

android {
  compileOptions {
      sourceCompatibility JavaVersion.VERSION_17
      targetCompatibility JavaVersion.VERSION_17
  }
}

apply from: "../capacitor-cordova-android-plugins/cordova.variables.gradle"
dependencies {
    implementation project(':capacitor-community-barcode-scanner')
    implementation project(':capacitor-app')
    implementation project(':capacitor-browser')
    implementation project(':capacitor-camera')
    implementation project(':capacitor-device')
    implementation project(':capacitor-filesystem')
    implementation project(':capacitor-geolocation')
    implementation project(':capacitor-haptics')
    implementation project(':capacitor-keyboard')
    implementation project(':capacitor-network')
    implementation project(':capacitor-share')
    implementation project(':capacitor-splash-screen')
    implementation project(':capacitor-status-bar')
    implementation project(':capawesome-team-capacitor-file-opener')
    implementation project(':capacitor-native-settings')
    implementation "com.onesignal:OneSignal:5.1.4"
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.7.10"
}
apply from: "../../node_modules/onesignal-cordova-plugin/build-extras-onesignal.gradle"

if (hasProperty('postBuildExtras')) {
  postBuildExtras()
}
